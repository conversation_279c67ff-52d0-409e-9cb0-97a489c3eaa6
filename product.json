{"nameShort": "<PERSON><PERSON><PERSON><PERSON>", "nameLong": "<PERSON><PERSON><PERSON><PERSON>", "applicationName": "kwai<PERSON>lot", "dataFolderName": ".kwai<PERSON>lot", "win32MutexName": "kwai<PERSON>lot", "licenseName": "MIT", "licenseUrl": "https://github.com/microsoft/vscode/blob/main/LICENSE.txt", "serverLicenseUrl": "https://github.com/microsoft/vscode/blob/main/LICENSE.txt", "serverGreeting": [], "serverLicense": [], "serverLicensePrompt": "", "serverApplicationName": "kwaipilot-server", "serverDataFolderName": ".kwaipilot-server", "tunnelApplicationName": "kwaipilot-tunnel", "win32DirName": "Microsoft Kwaipilot", "win32NameVersion": "Microsoft Kwaipilot", "win32RegValueName": "<PERSON><PERSON><PERSON><PERSON>", "win32x64AppId": "{{D77B7E06-80BA-4137-BCF4-654B95CCEBC5}", "win32arm64AppId": "{{D1ACE434-89C5-48D1-88D3-E2991DF85475}", "win32x64UserAppId": "{{CC6B787D-37A0-49E8-AE24-8559A032BE0C}", "win32arm64UserAppId": "{{3AEBF0C8-F733-4AD4-BADE-FDB816D53D7B}", "win32AppUserModelId": "Microsoft.Kwaipilot", "win32ShellNameShort": "<PERSON><PERSON><PERSON><PERSON>", "win32TunnelServiceMutex": "kwaipilot-tunnelservice", "win32TunnelMutex": "kwaipilot-tunnel", "darwinBundleIdentifier": "com.kuaishou.kwaipilot.editor", "darwinProfileUUID": "797E170A-2DE4-4675-AAAA-59C03F2418AA", "darwinProfilePayloadUUID": "C086C0A4-D3B1-459D-B268-96BDEA61077A", "linuxIconName": "kwai<PERSON>lot", "licenseFileName": "LICENSE.txt", "reportIssueUrl": "https://github.com/microsoft/vscode/issues/new", "nodejsRepository": "https://nodejs.org", "urlProtocol": "kwai<PERSON>lot", "webviewContentExternalBaseUrlTemplate": "https://{{uuid}}.vscode-cdn.net/insider/ef65ac1ba57f57f2a3961bfe94aa20481caca4c6/out/vs/workbench/contrib/webview/browser/pre/", "builtInExtensions": [{"name": "ms-vscode.js-debug-companion", "version": "1.1.3", "sha256": "7380a890787452f14b2db7835dfa94de538caf358ebc263f9d46dd68ac52de93", "repo": "https://github.com/microsoft/vscode-js-debug-companion", "metadata": {"id": "99cb0b7f-7354-4278-b8da-6cc79972169d", "publisherId": {"publisherId": "5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee", "publisherName": "ms-vscode", "displayName": "Microsoft", "flags": "verified"}, "publisherDisplayName": "Microsoft"}}, {"name": "ms-vscode.js-debug", "version": "1.97.1", "sha256": "977dd854805547702e312e176f68a1b142fa123f228258f47f0964560ad32496", "repo": "https://github.com/microsoft/vscode-js-debug", "metadata": {"id": "25629058-ddac-4e17-abba-74678e126c5d", "publisherId": {"publisherId": "5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee", "publisherName": "ms-vscode", "displayName": "Microsoft", "flags": "verified"}, "publisherDisplayName": "Microsoft"}}, {"name": "ms-vscode.vscode-js-profile-table", "version": "1.0.10", "sha256": "7361748ddf9fd09d8a2ed1f2a2d7376a2cf9aae708692820b799708385c38e08", "repo": "https://github.com/microsoft/vscode-js-profile-visualizer", "metadata": {"id": "7e52b41b-71ad-457b-ab7e-0620f1fc4feb", "publisherId": {"publisherId": "5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee", "publisherName": "ms-vscode", "displayName": "Microsoft", "flags": "verified"}, "publisherDisplayName": "Microsoft"}}], "updateUrl": "https://team-robot.corp.kuaishou.com", "downloadUrl": "https://team-robot.corp.kuaishou.com/download", "appVersion": "0.0.5", "vscodeVersion": "1.99.2", "quality": "dev", "linkProtectionTrustedDomains": ["https://kwaipilot.corp.kuaishou.com", "https://sso.corp.kuaishou.com", "*.corp.kuaishou.com"]}