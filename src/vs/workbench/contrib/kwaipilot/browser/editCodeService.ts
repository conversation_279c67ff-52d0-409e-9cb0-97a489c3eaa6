import { IEditCodeService, ShowDiffParams, AcceptDiffParams, RejectDiffParams, DiffAcceptedEvent, DiffRejectedEvent, DiffArea, Diff, UpdateDiffParams, StreamingState } from '../common/editCode.js';
import { IEditorService } from '../../../../workbench/services/editor/common/editorService.js';
import { Emitter } from '../../../../base/common/event.js';
import { registerSingleton, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
import { Disposable, DisposableStore } from '../../../../base/common/lifecycle.js';
import { ITextFileService } from '../../../services/textfile/common/textfiles.js';
import { IFileService } from '../../../../platform/files/common/files.js';
import { IEditorWorkerService } from '../../../../editor/common/services/editorWorker.js';
import { ICodeEditor, IViewZone, IContentWidget, ContentWidgetPositionPreference } from '../../../../editor/browser/editorBrowser.js';
import { IEditorDecorationsCollection } from '../../../../editor/common/editorCommon.js';
import { Range } from '../../../../editor/common/core/range.js';
import { ChatEditingModifiedDocumentEntry } from '../../../contrib/chat/browser/chatEditing/chatEditingModifiedDocumentEntry.js';
import { IModelService } from '../../../../editor/common/services/model.js';
import { ILogService } from '../../../../platform/log/common/log.js';
import { ChatEditingCodeEditorIntegration } from '../../../contrib/chat/browser/chatEditing/chatEditingCodeEditorIntegration.js';
import { URI } from '../../../../base/common/uri.js';
import { ModelDecorationOptions } from '../../../../editor/common/model/textModel.js';
import { MinimapPosition, OverviewRulerLane, IModelDeltaDecoration, ITextModel } from '../../../../editor/common/model.js';
import { themeColorFromId } from '../../../../base/common/themables.js';
import { RenderOptions, LineSource, renderLines } from '../../../../editor/browser/widget/diffEditor/components/diffEditorViewZones/renderLines.js';
import { overviewRulerAddedForeground, minimapGutterAddedBackground, overviewRulerDeletedForeground, minimapGutterDeletedBackground } from '../../../../workbench/contrib/scm/common/quickDiff.js';
import { IKwaiPilotBridgeAPIService } from '../common/kwaiPilotBridgeAPIService.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { relativePath as getRelativePath } from '../../../../base/common/resources.js';

import './media/editCodeService.css';
import { IDocumentDiff } from '../../../../editor/common/diff/documentDiffProvider.js';
import { DetailedLineRangeMapping } from '../../../../editor/common/diff/rangeMapping.js';
import { WEBVIEW_BRIDGE_EVENT_NAME } from './kwaiPilotBridgeAPIService.js';


/**
 * Service for handling code editing and diff functionality in KwaiPilot.
 * Manages the lifecycle of document entries and editor integrations.
 */
export class EditCodeService extends Disposable implements IEditCodeService {
	_serviceBrand: undefined;

	private readonly _onDiffAccepted = this._register(new Emitter<DiffAcceptedEvent>());
	readonly onDiffAccepted = this._onDiffAccepted.event;

	private readonly _onDiffRejected = this._register(new Emitter<DiffRejectedEvent>());
	readonly onDiffRejected = this._onDiffRejected.event;

	private readonly _onDidAddOrDeleteDiffAreas = this._register(new Emitter<{ uri: URI }>());
	readonly onDidAddOrDeleteDiffAreas = this._onDidAddOrDeleteDiffAreas.event;

	private readonly _onDidChangeDiffsInArea = this._register(new Emitter<{ uri: URI; areaId: string }>());
	readonly onDidChangeDiffsInArea = this._onDidChangeDiffsInArea.event;

	private readonly _onDidChangeStreamingInArea = this._register(new Emitter<{ uri: URI; areaId: string }>());
	readonly onDidChangeStreamingInArea = this._onDidChangeStreamingInArea.event;

	private readonly documentEntries = new Map<string, ChatEditingModifiedDocumentEntry>();
	private readonly editorIntegrations = new Map<string, ChatEditingCodeEditorIntegration>();
	private readonly diffAreas = new Map<string, DiffArea>();
	private readonly diffs = new Map<string, any>(); // 兼容 Monaco diff
	private readonly streamingStates = new Map<string, {
		originalModel: ITextModel;
		modifiedModel: ITextModel;
		currentContent: string;
		initialContent: string; // 新增，保存最初的原始内容
		decorations: IEditorDecorationsCollection;
		viewZones: string[];
		disposables: DisposableStore;
	}>();
	private readonly diffDecorators = new Map<string, DisposableStore>();

	private _currentStreamingState: StreamingState | undefined;

	private isStreaming: boolean = false;
	private isRestoring: boolean = false; // 用于标记是否正在恢复状态

	private diffFooterDom: HTMLDivElement | undefined;
	private currentDiffIndex: number = 0;

	private isProgrammaticScroll: boolean = false;

	// 记录每个文件下 chunk 的 accept 状态
	private readonly chunkAcceptStates = new Map<string, Map<string, boolean>>(); // uri -> (chunkId -> accepted)

	// 记录每个文件下 chunk 的 reject 状态
	private readonly chunkRejectStates = new Map<string, Map<string, boolean>>(); // uri -> (chunkId -> rejected)

	// 新增：浮动按钮容器
	private floatingChunkWidgets: {
		id: string;
		widget: IContentWidget;
		chunkId: string;
		diffEntry: DetailedLineRangeMapping;
		domNode: HTMLElement;
		viewZoneId?: number;
	}[] = [];
	private decorationCollection: {
		diffEntry: DetailedLineRangeMapping;
		decorationIds?: number[];
	}[] = [];

	// 滚动和布局监听器
	private scrollAndLayoutListeners: DisposableStore | undefined;

	// 快捷键监听器
	private _keybindingDisposables: DisposableStore | undefined;

	constructor(
		@IEditorService private readonly editorService: IEditorService,
		@ITextFileService private readonly textFileService: ITextFileService,
		@IFileService private readonly fileService: IFileService,
		@IEditorWorkerService private readonly editorWorkerService: IEditorWorkerService,
		@IModelService private readonly modelService: IModelService,
		@ILogService private readonly logService: ILogService,
		@IKwaiPilotBridgeAPIService private readonly kwaiPilotBridgeAPIService: IKwaiPilotBridgeAPIService,
		@IWorkspaceContextService private readonly workspaceContextService: IWorkspaceContextService,
	) {
		super();

		this._onDidAddOrDeleteDiffAreas.event(({ uri }) => {
			// this.updateDiffDecorations(uri);
			// this.updateDiffFooter(uri);
		});

		// 监听编辑器切换，确保浮层正确刷新/消失
		this.editorService.onDidActiveEditorChange(() => {
			const editor = this.editorService.activeTextEditorControl as ICodeEditor;
			const model = editor?.getModel();
			if (model) {
				const uri = model.uri;
				this.updateDiffDecorations(uri);
				this.updateDiffFooter(uri);
			} else {
				this.removeDiffFooter();
				this.removeChunkButtons();
			}
		});

		this.fileService.onDidFilesChange(e => {
			if (this.isRestoring) {
				this.logService.info('[KwaiPilot] File change detected during restoring, skipping diff update');
				return;
			}
			if (this.isStreaming) {
				return;
			}
			const uris: string[] = [];
			if (e.rawUpdated) uris.push(...e.rawUpdated.map(u => u.toString()));
			if (e.rawAdded) uris.push(...e.rawAdded.map(u => u.toString()));
			if (e.rawDeleted) uris.push(...e.rawDeleted.map(u => u.toString()));
			for (const uri of uris) {
				this.logService.info(`[KwaiPilot] File change detected: ${uri}`);
				const diffAreas = this.getDiffAreas(URI.parse(uri));
				if (diffAreas && diffAreas.length > 0) {
					this.logService.info(`[KwaiPilot] Refreshing diff for changed file: ${uri}`);
					// 使用 textFileService 重新读取文件内容
					this.textFileService.read(URI.parse(uri)).then(fileContent => {
						if (fileContent) {
							// 然后再刷新 diff
							this.refreshDiffOnFileChange(URI.parse(uri), fileContent.value);
						}
					});

				}
			}
		});


		this.kwaiPilotBridgeAPIService.bridge.addMessageListener(WEBVIEW_BRIDGE_EVENT_NAME.COMPOSER_STATE_UPDATE, (composerState: any) => {
			this.handleComposerStateUpdate(composerState);
		});
	}

	/**
	 * 处理 Composer 状态更新，仅处理文件的 accepted 和 rejected 状态
	 * 根据 COMPOSER_STATE_UPDATE 事件中的信息，处理文件被接受或拒绝的情况
	 * @param composerState Composer 状态信息，包含 localMessages 和 indeterminatedWorkingSetEffects
	 */
	private handleComposerStateUpdate(composerState: any) {
		try {
			this.logService.info(`[KwaiPilot] handleComposerStateUpdate: sessionId=${composerState.sessionId}, messages=${composerState.localMessages?.length || 0}`);

			// 基础验证：确保有必要的数据
			if (!composerState || !Array.isArray(composerState.localMessages)) {
				this.logService.error(`[KwaiPilot] Invalid ComposerState received, skipping update`);
				return;
			}

			// 只处理当前工作区的会话
			if (!composerState.isCurrentWorkspaceSession) {
				this.logService.info(`[KwaiPilot] Skipping non-current workspace session`);
				return;
			}

			// 提取编辑文件相关的消息
			const editFileMessages = this.getEditFileMessages(composerState.localMessages || []);
			const indeterminatedEffects = composerState.indeterminatedWorkingSetEffects || [];

			this.logService.info(`[KwaiPilot] Found ${editFileMessages.length} edit file messages, ${indeterminatedEffects.length} indeterminated effects`);

			// 处理文件的 accepted 和 rejected 状态
			this.handleFileAcceptRejectStates(editFileMessages, indeterminatedEffects);

		} catch (error) {
			this.logService.error(`[KwaiPilot] handleComposerStateUpdate error:`, error);
		}
	}

	/**
	 * 处理文件的 accepted 和 rejected 状态
	 */
	private handleFileAcceptRejectStates(editFileMessages: any[], indeterminatedEffects: any[]) {
		// 构建中间状态映射表
		const indeterminatedMap = new Map<number, any>();
		indeterminatedEffects.forEach(effect => {
			indeterminatedMap.set(effect.messageTs, effect);
		});

		// 按文件路径分组消息
		const filePathToMessages = new Map<string, any[]>();
		editFileMessages.forEach(message => {
			if (message.workingSetEffect?.path) {
				const path = message.workingSetEffect.path;
				if (!filePathToMessages.has(path)) {
					filePathToMessages.set(path, []);
				}
				filePathToMessages.get(path)!.push(message);
			}
		});

		// 处理每个文件的状态，只关注 accepted 和 rejected
		filePathToMessages.forEach((messages, filePath) => {
			this.processFileAcceptRejectState(filePath, messages, indeterminatedMap);
		});
	}

	/**
	 * 处理单个文件的 accepted/rejected 状态
	 */
	private processFileAcceptRejectState(filePath: string, messages: any[], indeterminatedMap: Map<number, any>) {
		try {
			// 获取最新的消息状态
			const indeterminatedMessage = messages.find(msg => indeterminatedMap.has(msg.ts));
			const bestMessage = indeterminatedMessage || messages[messages.length - 1];

			if (!bestMessage) {
				this.logService.warn(`[KwaiPilot] No valid message found for file: ${filePath}`);
				return;
			}

			const workspaceFolder = this.workspaceContextService.getWorkspace().folders[0];
			if (!workspaceFolder) {
				this.logService.warn(`[KwaiPilot] No workspace folder found`);
				return;
			}

			const absolutePath = URI.joinPath(workspaceFolder.uri, filePath);
			const indeterminatedEffect = indeterminatedMap.get(bestMessage.ts);

			// 获取当前状态：优先使用中间状态，然后是持久化状态
			const currentState = indeterminatedEffect?.state || bestMessage.workingSetEffect?.status;

			this.logService.info(`[KwaiPilot] processFileAcceptRejectState: ${filePath}, state=${currentState}, messageTs=${bestMessage.ts}`);

			// 只处理 accepted 和 rejected 状态
			switch (currentState) {
				case 'accepted':
					// 文件已被接受，调用 acceptDiff 方法
					this.logService.info(`[KwaiPilot] File ${filePath} accepted, calling acceptDiff`);
					this.acceptDiff({ uri: absolutePath });
					break;

				case 'rejected':
					// 文件已被拒绝，调用 rejectDiff 方法
					this.logService.info(`[KwaiPilot] File ${filePath} rejected, calling rejectDiff`);
					this.rejectDiff({ uri: absolutePath });
					break;

				default:
					// 其他状态不处理，保持原有状态
					this.logService.info(`[KwaiPilot] File ${filePath} in state '${currentState}', no action needed`);
					break;
			}
		} catch (error) {
			this.logService.error(`[KwaiPilot] Error processing file accept/reject state for ${filePath}:`, error);
		}
	}

	/**
	 * 提取编辑文件类型的消息
	 */
	private getEditFileMessages(localMessages: any[]): any[] {
		return localMessages.filter(message =>
			message.type === 'say' &&
			message.say === 'tool' &&
			message.workingSetEffect?.path
		);
	}

	private refreshDiffOnFileChange(uri: URI, newContent: string) {
		const streamingState = this.streamingStates.get(uri.path.toString());
		if (!streamingState) {
			this.removeDiffFooter();
			this.removeChunkButtons();
			return;
		}

		if (newContent === streamingState.currentContent) {
			return;
		}
		// 更新当前流式状态
		this._currentStreamingState = {
			uri,
			content: newContent,
			initialContent: streamingState.initialContent,
			isFinal: true
		};

		this.streamingStates.set(uri.path.toString(), {
			...streamingState,
			currentContent: newContent,
		});

		this._updateDiffContent(this._currentStreamingState, false);
		this.updateDiffFooter(uri);
	}

	private async initStreamingState(params: {
		uri: URI;
		initialContent: string;
		currentContent: string;
	}): Promise<{
		originalModel: ITextModel;
		modifiedModel: ITextModel;
		currentContent: string;
		initialContent: string;
		decorations: IEditorDecorationsCollection;
		viewZones: string[];
		disposables: DisposableStore;
	} | undefined> {
		const { uri, initialContent, currentContent } = params;
		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		if (!editor) {
			this.logService.error('[KwaiPilot] initStreamingState: No active editor found');
			return undefined;
		}

		// Create temporary models for diff
		const tempOriginalUri = uri.with({ scheme: 'kwaipilot-diff-original' });
		const tempModifiedUri = uri.with({ scheme: 'kwaipilot-diff-modified' });

		const existingOriginalModel = this.modelService.getModel(tempOriginalUri);
		if (existingOriginalModel) {
			this.modelService.destroyModel(tempOriginalUri);
		}

		const existingModifiedModel = this.modelService.getModel(tempModifiedUri);
		if (existingModifiedModel) {
			this.modelService.destroyModel(tempModifiedUri);
		}

		const mainModel = this.modelService.getModel(uri);
		const language = mainModel ? mainModel.getLanguageId() : null;

		const originalModel = this.modelService.createModel(
			initialContent,
			language as any,
			tempOriginalUri,
			true
		);

		const modifiedModel = this.modelService.createModel(
			currentContent,
			language as any,
			tempModifiedUri,
			true
		);

		const state = {
			originalModel,
			modifiedModel,
			currentContent,
			initialContent, // 新增
			decorations: editor.createDecorationsCollection(),
			viewZones: [],
			disposables: new DisposableStore()
		};

		this.logService.info(`[KwaiPilot] initStreamingState: Created temp models for diff`);
		return state;
	}

	// 1. 监听主 model 内容变更，实时刷新 diff
	private registerModelContentListener(model: ITextModel) {
		const uriStr = model.uri.path.toString();
		if (!this.diffDecorators.has(uriStr)) {
			const disposables = new DisposableStore();
			disposables.add(model.onDidChangeContent(() => {
				if (this.isRestoring) {
					this.logService.info('[KwaiPilot] File content changed during restoring, skip updateDiffDecorations/updateDiffFooter');
					return;
				}
				this.logService.info(`[KwaiPilot] File content changed: ${uriStr}`);
				this.updateDiffDecorations(model.uri);
				this.updateDiffFooter(model.uri);
			}));
			this.diffDecorators.set(uriStr, disposables);
		}
	}

	/**
	 * Shows a diff of the proposed changes in the current editor.
	 * Creates a new document entry if one doesn't exist and sets up the editor integration.
	 * The diff is shown inline using decorations and viewzones.
	 *
	 * @param params Parameters containing the original and modified content
	 */
	async showDiff(params: ShowDiffParams): Promise<void> {
		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		if (!editor) {
			this.logService.error('[KwaiPilot] showDiff: No active editor found');
			return;
		}

		const model = editor.getModel();
		if (!model || model.uri.toString() !== params.uri.toString()) {
			this.logService.error('[KwaiPilot] showDiff: No model found in editor or URI mismatch');
			return;
		}

		this.registerModelContentListener(model);

		this.logService.info(`[KwaiPilot] showDiff: Start for uri=${params.uri.toString()}`);

		// Store original content for potential revert
		const originalContent = model.getValue();

		// Initialize streaming state if not exists
		let state = this.streamingStates.get(params.uri.path.toString());
		if (!state) {
			state = await this.initStreamingState({
				uri: params.uri,
				initialContent: originalContent,
				currentContent: params.modifiedContent || '',
			});
			if (!state) {
				return;
			}
			this.streamingStates.set(params.uri.path.toString(), state);
		}

		if (!params.modifiedContent) {
			this.logService.info('[KwaiPilot] showDiff: No modified content, skipping diff display');
			return;
		}

		// 计算差异
		const diff = await this.editorWorkerService.computeDiff(
			state.originalModel.uri,
			state.modifiedModel.uri,
			{ computeMoves: true, ignoreTrimWhitespace: false, maxComputationTimeMs: Number.MAX_SAFE_INTEGER },
			'advanced'
		);

		this.logService.info(`[KwaiPilot] showDiff: Diff computed, identical=${diff?.identical}, changes=${diff?.changes?.length}`);

		if (!diff || diff.identical || !diff.changes || diff.changes.length === 0) {
			this.logService.info('[KwaiPilot] showDiff: No differences found, disposing models');
			state.disposables.dispose();
			this.streamingStates.delete(params.uri.path.toString());
			return;
		}

		// Store the original content and diff information for potential revert
		this.genDiffArea(params.uri, diff, originalContent);

		// 渲染 view zone 和装饰器
		editor.changeViewZones(viewZoneChangeAccessor => {
			for (const id of state.viewZones) {
				viewZoneChangeAccessor.removeZone(id);
			}
			state.viewZones.length = 0;
		});
		this.renderDiffViewZones(diff, editor, state.originalModel, state.modifiedModel, state.viewZones, false);

		this.logService.info('[KwaiPilot] showDiff: Finished, decorations and view zones applied');

		// 通知 diff 区域变更
		this._onDidAddOrDeleteDiffAreas.fire({ uri: params.uri });
	}

	private createDiffDecorations(diff: IDocumentDiff, editor: ICodeEditor) {
		// 清空之前的装饰器，避免叠加
		const model = editor.getModel();
		const decorationsCollection = editor.createDecorationsCollection();
		decorationsCollection.set([]); // 清空所有旧装饰器
		this.decorationCollection = []; // 清空之前的装饰器记录

		this.logService.info(`[KwaiPilot][Log][createDiffDecorations] ENTER diff.changes.length=${diff.changes?.length}`);
		this.logService.info(`[KwaiPilot][Log][createDiffDecorations] model.uri=${model?.uri.path.toString()}, lineCount=${model?.getLineCount()}`);

		const decorations: IModelDeltaDecoration[] = [];

		const decorationIds: number[] = [];

		// 关键日志：确认 editor 当前 model 与 decorations 目标 model

		// 创建行级装饰器选项
		const diffLineAddDecorationBackground = ModelDecorationOptions.createDynamic({
			className: 'line-insert',
			description: 'line-insert',
			isWholeLine: true,
			marginClassName: 'gutter-insert',
			overviewRuler: {
				color: themeColorFromId(overviewRulerAddedForeground),
				position: OverviewRulerLane.Left
			},
			minimap: {
				color: themeColorFromId(minimapGutterAddedBackground),
				position: MinimapPosition.Gutter
			}
		});

		const diffLineDeleteDecorationBackground = ModelDecorationOptions.createDynamic({
			className: 'line-delete',
			description: 'line-delete',
			isWholeLine: true,
			marginClassName: 'gutter-delete',
			overviewRuler: {
				color: themeColorFromId(overviewRulerDeletedForeground),
				position: OverviewRulerLane.Left
			},
			minimap: {
				color: themeColorFromId(minimapGutterDeletedBackground),
				position: MinimapPosition.Gutter
			}
		});

		// 为每个变更创建装饰器
		for (const [idx, change] of diff.changes.entries()) {
			this.logService.info(`[KwaiPilot][Log][DiffDecorations] [${idx}] original: [${change.original.startLineNumber},${change.original.endLineNumberExclusive}), modified: [${change.modified.startLineNumber},${change.modified.endLineNumberExclusive}), original.isEmpty=${change.original.isEmpty}, modified.isEmpty=${change.modified.isEmpty}`);
			// 只在纯删除时渲染删除装饰器
			if ((change.original && !change.original.isEmpty) && (change.modified && change.modified.isEmpty)) {
				for (let line = change.original.startLineNumber; line < change.original.endLineNumberExclusive; line++) {
					this.logService.info(`[KwaiPilot][Log][DiffDecorations] [${idx}] Delete decoration: line ${line}`);
					decorationIds.push(decorations.push({
						range: new Range(line, 1, line, 1),
						options: diffLineDeleteDecorationBackground
					}));
				}
			} else if (change.modified && !change.modified.isEmpty && (change.original && change.original.isEmpty)) {
				// 只在纯新增时渲染新增装饰器
				for (let line = change.modified.startLineNumber; line < change.modified.endLineNumberExclusive; line++) {
					this.logService.info(`[KwaiPilot][Log][DiffDecorations] [${idx}] Add decoration: line ${line} (modified: [${change.modified.startLineNumber},${change.modified.endLineNumberExclusive}))`);
					decorationIds.push(decorations.push({
						range: new Range(line, 1, line, 1),
						options: diffLineAddDecorationBackground
					}));
				}
			} else if ((change.original && !change.original.isEmpty) && (change.modified && !change.modified.isEmpty)) {
				// 修改（替换）类型：原始区间渲染删除，新区间渲染新增
				for (let line = change.modified.startLineNumber; line < change.modified.endLineNumberExclusive; line++) {
					decorationIds.push(decorations.push({
						range: new Range(line, 1, line, 1),
						options: diffLineAddDecorationBackground
					}));
				}
			}
			this.decorationCollection.push({
				diffEntry: change,
				decorationIds: decorationIds
			});
		}
		// 新增日志：装饰器分布统计
		const lineDecorMap: Record<number, number> = {};
		for (const decor of decorations) {
			const line = decor.range.startLineNumber;
			lineDecorMap[line] = (lineDecorMap[line] || 0) + 1;
		}
		this.logService.info(`[KwaiPilot][Log][DiffDecorations] lineDecorMap: ${JSON.stringify(lineDecorMap)}`);
		this.logService.info(`[KwaiPilot][Log][createDiffDecorations] EXIT decorations.length=${decorations.length}, lineDecorMap: ${JSON.stringify(lineDecorMap)}`);
		return decorations;
	}

	/**
	 * Accepts the current diff changes.
	 * This will remove the diff decorations since the changes are already in the file.
	 *
	 * @param params Parameters for accepting the diff
	 */
	async acceptDiff(params: AcceptDiffParams): Promise<void> {
		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		if (!editor) {
			this.logService.info('[KwaiPilot] acceptDiff: No active editor found');
			return;
		}

		const model = editor.getModel();
		if (!model) {
			this.logService.info('[KwaiPilot] acceptDiff: No model found in editor');
			return;
		}

		if (!params.uri) {
			this.logService.info('[KwaiPilot] acceptDiff: No URI provided in params');
			return;
		}

		// 获取对应的 diff 区域
		const diffArea = this.getDiffAreas(params.uri)[0];

		if (!diffArea) {
			this.logService.info('[KwaiPilot] acceptDiff: No diff area found for the given URI');
			return;
		}
		this.isRestoring = true; // 加锁

		this.clearAllDiffState(params.uri!);


		// 触发事件
		this._onDiffAccepted.fire({
			uri: params.uri!,
			changes: diffArea.diffs.map(diff => ({
				originalRange: diff.originalRange,
				modifiedRange: diff.modifiedRange,
				content: diff.content
			}))
		});
		this.refreshGitScm(params.uri!);
		this.isRestoring = false; // 解锁
	}

	/**
	 * Rejects the current diff changes.
	 * This will revert the file to its original state and clean up the diff decorations.
	 *
	 * @param params Parameters for rejecting the diff
	 */
	async rejectDiff(params: RejectDiffParams): Promise<void> {
		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		if (!editor) {
			this.logService.info('[KwaiPilot] rejectDiff: No active editor found');
			return;
		}

		const model = editor.getModel();
		if (!model) {
			this.logService.info('[KwaiPilot] rejectDiff: No model found in editor');
			return;
		}

		// 获取对应的 diff 区域
		const diffArea = this.getDiffAreas(params.uri!)[0];

		if (!diffArea) {
			this.logService.info('[KwaiPilot] rejectDiff: No diff area found for the given URI');
			return;
		}

		// 恢复原始内容
		try {
			this.isRestoring = true; // 加锁
			// 先恢复主 model 内容
			model.setValue(diffArea.diffs[0].content);
			await this.textFileService.write(params.uri!, diffArea.diffs[0].content, {
				writeElevated: false
			});
			this.logService.info('[KwaiPilot] rejectDiff: Successfully reverted file content');
		} catch (error) {
			this.isRestoring = false; // 解锁
			this.logService.error(`[KwaiPilot] rejectDiff: Failed to revert file: ${error}`);
			return;
		}

		this.clearAllDiffState(params.uri!);

		// 触发事件
		this._onDiffRejected.fire({
			uri: params.uri!,
			range: diffArea.diffs[0].modifiedRange
		});
		this.isRestoring = false; // 解锁
	}

	private async refreshGitScm(uri: URI) {
		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		if (!editor) {
			this.logService.info('[KwaiPilot] rejectDiff: No active editor found');
			return;
		}
		const model = editor.getModel();
		if (!model) {
			this.logService.info('[KwaiPilot] rejectDiff: No model found in editor');
			return;
		}
		// 主动刷新 git/scm
		try {
			model.setValue(model.getValue());
			await this.textFileService.save(uri);
		} catch (e) {
			this.logService.warn('[KwaiPilot] this.textFileService.save failed:', e);
			this.isRestoring = false;
		}
	}

	private async clearAllDiffState(uri: URI): Promise<void> {
		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		if (!editor) {
			this.logService.info('[KwaiPilot] rejectDiff: No active editor found');
			return;
		}

		const model = editor.getModel();
		if (!model) {
			this.logService.info('[KwaiPilot] rejectDiff: No model found in editor');
			return;
		}

		// 获取对应的 diff 区域
		const diffAreas = this.getDiffAreas(uri);

		// 清理主 model 的 diff 装饰器
		if (model) {
			const allDecorations = model.getAllDecorations();
			if (allDecorations.length > 0) {
				model.deltaDecorations(allDecorations.map(d => d.id), []);
				// 清除之后
				this.refreshGitScm(uri);
			}
		}


		if (!diffAreas || diffAreas.length === 0) {
			this.logService.info('[KwaiPilot] rejectDiff: No diff area found for the given URI');
		}
		// 清理装饰器
		const disposables = this.diffDecorators.get(uri.path.toString());
		if (disposables) {
			disposables.dispose();
			this.diffDecorators.delete(uri.path.toString());
		}

		if (diffAreas && diffAreas.length > 0) {
			diffAreas.forEach(diffArea => {
				if (diffArea.uri.path === uri.path) {
					// 移除 diff 区域
					this.logService.info(`[KwaiPilot] clearAllDiffState: Removing diff area with id=${diffArea.id}`);
					this.diffAreas.delete(diffArea.id);
					this.diffs.delete(diffArea.id);
				}
				this._onDidAddOrDeleteDiffAreas.fire({ uri: diffArea.uri });
			});
		}


		const streamingState = this.streamingStates.get(uri.path.toString());

		// 清理 decorationsCollection
		if (streamingState?.decorations) {
			streamingState.decorations.set([]);
		}

		if (streamingState && streamingState.viewZones.length > 0) {
			editor.changeViewZones(viewZoneChangeAccessor => {
				for (const id of streamingState.viewZones) {
					viewZoneChangeAccessor.removeZone(id);
				}
				streamingState.viewZones.length = 0;
			});
		}
		this.streamingStates.delete(uri.path.toString());

		// 清理 footer和浮动按钮
		this.removeChunkButtons();
		this.removeDiffFooter();
		this.clearChunkAcceptStates(uri);

		this.diffDecorators.delete(uri.path.toString());
		this.chunkAcceptStates.delete(uri.path.toString());
		this.chunkRejectStates.delete(uri.path.toString());

		// 确保文件保存
		try {
			this.isRestoring = true;
			await this.textFileService.save(uri);
		} catch (e) {
			this.logService.error('[KwaiPilot] Failed to save file during clearAllDiffState:', e);
		} finally {
			this.isRestoring = false;
		}
	}

	/**
	 * Gets all diff areas for a given URI.
	 *
	 * @param uri The URI to get diff areas for
	 */
	getDiffAreas(uri: URI): DiffArea[] {
		return Array.from(this.diffAreas.values()).filter(area => area.uri.path === uri.path);
	}

	/**
	 * Gets a diff by its ID.
	 *
	 * @param id The ID of the diff to get
	 */
	getDiff(id: string): Diff | undefined {
		return this.diffs.get(id);
	}
	/**
	 * Accepts all diffs in all files.
	 */
	async acceptAllDiffs(): Promise<void> {
		for (const diffArea of this.diffAreas.values()) {
			await this.acceptDiff({ uri: diffArea.uri });
		}
		this.logService.info('[KwaiPilot] All diffs accepted and cleared');
	}

	/**
	 * Rejects all diffs in all files.
	 */
	async rejectAllDiffs(): Promise<void> {
		for (const diffArea of this.diffAreas.values()) {
			await this.rejectDiff({ uri: diffArea.uri });
		}
		this.logService.info('[KwaiPilot] All diffs rejected and cleared');

	}

	/**
	 * Disposes of all document entries and editor integrations.
	 * Cleans up any remaining decorations and viewzones.
	 */
	override dispose(): void {
		for (const [_, entry] of this.documentEntries) {
			entry.dispose();
		}
		this.documentEntries.clear();

		for (const [_, integration] of this.editorIntegrations) {
			integration.dispose();
		}
		this.editorIntegrations.clear();

		this.logService.info('[KwaiPilot] Disposing EditCodeService');
		this.diffAreas.clear();
		this.diffs.clear();

		for (const [_, disposables] of this.diffDecorators) {
			disposables.dispose();
		}
		this.diffDecorators.clear();

		super.dispose();
	}

	/**
	 * 更新 diff 内容
	 * @param uri 文件 URI
	 * @param content 更新内容
	 * @param isFinal 是否是最终更新
	 */
	async updateDiff(params: UpdateDiffParams): Promise<void> {
		const { uri, content, isFinal } = params;
		this.logService.info(`[KwaiPilot][Log] updateDiff: Start for content=${content}, isFinal=${isFinal}`);

		this.isStreaming = true;

		// 获取或初始化 streaming state
		let streamingState = this.streamingStates.get(uri.path.toString());
		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		if (editor) {
			this.setStreamingMask(editor, !isFinal);
		}
		// 获取或初始化 streaming state
		if (!streamingState) {
			const editor = this.editorService.activeTextEditorControl as ICodeEditor;
			if (!editor) {
				this.logService.error('[KwaiPilot][Log] updateDiff: No active editor found');
				return;
			}
			const model = editor.getModel();
			if (!model) {
				this.logService.error('[KwaiPilot][Log] updateDiff: No model found in editor');
				return;
			}
			this.logService.info(`[KwaiPilot][Log] updateDiff: New content=${content}`);
			this.logService.info(`[KwaiPilot][Log] updateDiff: Original content=${model.getValue()}`);
			streamingState = await this.initStreamingState({
				uri,
				initialContent: model.getValue(),
				currentContent: content,
			});
			if (!streamingState) {
				return;
			}
			this.streamingStates.set(uri.path.toString(), streamingState);
		}

		// 更新当前流式状态
		this._currentStreamingState = {
			uri,
			content,
			initialContent: streamingState.initialContent,
			isFinal
		};

		// 直接更新 diff 内容
		await this._updateDiffContent(this._currentStreamingState);

		if (isFinal) {
			this.logService.info('[KwaiPilot] updateDiff: Final update received');
			this.isStreaming = false;
			this.updateDiffFooter(uri);
		}
	}

	/** 获取 diffArea */
	private genDiffArea(uri: URI, diff: IDocumentDiff, initialContent: string): DiffArea {
		// 先清空当前 uri 对应的 diffArea
		const diffAreas = this.getDiffAreas(uri);
		if (diffAreas.length) {
			diffAreas.forEach(area => {
				this.diffAreas.delete(area.id);
			});
		}

		const areaId = `diff-area-${Date.now()}`;
		const diffArea: DiffArea = {
			id: areaId,
			uri: uri,
			startLine: diff.changes[0]?.modified.startLineNumber ?? 0,
			endLine: diff.changes[diff.changes.length - 1]?.modified.endLineNumberExclusive ?? 0,
			diffs: diff.changes.map(change => ({
				id: `${areaId}-${change.modified.startLineNumber}`,
				areaId,
				originalRange: new Range(
					change.original.startLineNumber,
					1,
					change.original.endLineNumberExclusive,
					1
				),
				modifiedRange: new Range(
					change.modified.startLineNumber,
					1,
					change.modified.endLineNumberExclusive,
					1
				),
				content: initialContent,
				change
			}))
		};

		// 存储 diff 区域
		this.diffAreas.set(areaId, diffArea);
		this.diffs.set(areaId, diff);

		return diffArea;
	}

	private async _updateDiffContent(state: StreamingState, writeFile: boolean = true) {
		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		if (!editor) {
			this.logService.info('[KwaiPilot][Log] _updateDiffContent: No active editor');
			return;
		}

		const streamingState = this.streamingStates.get(state.uri.path.toString());
		if (!streamingState) {
			this.logService.info('[KwaiPilot][Log] _updateDiffContent: No streamingState for uri ' + state.uri.path.toString());
			return;
		}

		// 新增日志：setValue 前后 model 状态
		this.logService.info(`[KwaiPilot][Log][DiffDecorations] BEFORE setValue: editor.getModel().uri=${editor.getModel()?.uri.toString()}, lineCount=${editor.getModel()?.getLineCount()}`);
		this.logService.info(`[KwaiPilot][Log][DiffDecorations] BEFORE setValue: streamingState.modifiedModel.uri=${streamingState.modifiedModel.uri.toString()}, lineCount=${streamingState.modifiedModel.getLineCount()}`);

		// 流式 diff：原始内容不截断，避免补空行，保证 diff 算法能正确识别新增/替换行
		const newLines = state.content.split('\n');
		const originalLines = state.initialContent.split('\n');
		const truncatedOriginal = originalLines.join('\n');
		const truncatedNew = newLines.join('\n');
		this.logService.info(`[KwaiPilot][Log] _updateDiffContent: newLines.length=${newLines.length}, originalLines.length=${originalLines.length}`);
		this.logService.info(`[KwaiPilot][Log] _updateDiffContent: truncatedOriginal(0~200)=${truncatedOriginal.slice(0, 200)}`);
		this.logService.info(`[KwaiPilot][Log] _updateDiffContent: truncatedNew(0~200)=${truncatedNew.slice(0, 200)}`);
		streamingState.currentContent = state.content;
		streamingState.modifiedModel.setValue(truncatedNew);
		streamingState.originalModel.setValue(truncatedOriginal);

		this.logService.info(`[KwaiPilot][Log][DiffDecorations] AFTER setValue: editor.getModel().uri=${editor.getModel()?.uri.toString()}, lineCount=${editor.getModel()?.getLineCount()}`);
		this.logService.info(`[KwaiPilot][Log][DiffDecorations] AFTER setValue: streamingState.modifiedModel.uri=${streamingState.modifiedModel.uri.toString()}, lineCount=${streamingState.modifiedModel.getLineCount()}`);


		if (state.isFinal) {
			if (writeFile) {
				try {
					await this.textFileService.write(state.uri, state.content, {
						writeElevated: false
					});
					this.logService.info('[KwaiPilot] updateDiff: Successfully wrote final content to file');
				} catch (error) {
					this.logService.error(`[KwaiPilot] updateDiff: Failed to write file: ${error}`);
					return;
				}

			}

			this.logService.info(`[KwaiPilot][Log] _updateDiffContent: final content.length=${state.content.length}`);

			streamingState.originalModel.setValue(state.initialContent);
			streamingState.modifiedModel.setValue(state.content);
			const diff = await this.editorWorkerService.computeDiff(
				streamingState.originalModel.uri,
				streamingState.modifiedModel.uri,
				{ computeMoves: true, ignoreTrimWhitespace: false, maxComputationTimeMs: Number.MAX_SAFE_INTEGER },
				'advanced'
			);
			this.logService.info(`[KwaiPilot] updateDiff: Final diff: ${JSON.stringify(diff)} (changes.length=${diff?.changes?.length})`);

			if (!diff || diff.identical || !diff.changes || diff.changes.length === 0) {
				this.logService.info('[KwaiPilot] No differences found in final update');
				// 清空当前文件的 diffAreas
				this.clearAllDiffState(state.uri);
				return;
			}

			const decorationsData = this.createDiffDecorations(diff, editor);
			this.logService.info(`[KwaiPilot][Log][DiffDecorations] BEFORE decorations.set: editor.getModel().uri=${editor.getModel()?.uri.toString()}, lineCount=${editor.getModel()?.getLineCount()}`);
			streamingState.decorations.set(decorationsData);
			this.logService.info(`[KwaiPilot][Log][DiffDecorations] AFTER decorations.set: editor.getModel().uri=${editor.getModel()?.uri.toString()}, lineCount=${editor.getModel()?.getLineCount()}`);
			this.logService.info(`[KwaiPilot][Log] updateDiff: viewZones.length=${streamingState.viewZones.length}`);

			this.genDiffArea(state.uri, diff, state.initialContent);
			// 通知 diff 区域变更
			this._onDidAddOrDeleteDiffAreas.fire({ uri: state.uri });

			// 清理流式状态
			this.logService.info('[KwaiPilot] Final update received, cleaning up streaming state');
			// 只清理 view zone，不删除 streamingState
			editor.changeViewZones((viewZoneChangeAccessor: { removeZone: (id: string) => void; addZone: (zone: IViewZone) => string }) => {
				for (const id of streamingState.viewZones) {
					viewZoneChangeAccessor.removeZone(id);
				}
				streamingState.viewZones.length = 0;
			});
			this.renderDiffViewZones(diff, editor, streamingState.originalModel, streamingState.modifiedModel, streamingState.viewZones, state.isFinal);
		} else {
			// 流式 diff（只到新代码最后一行）
			const diff = await this.editorWorkerService.computeDiff(
				streamingState.originalModel.uri,
				streamingState.modifiedModel.uri,
				{ computeMoves: true, ignoreTrimWhitespace: false, maxComputationTimeMs: Number.MAX_SAFE_INTEGER },
				'advanced'
			);
			this.logService.info(`[KwaiPilot][Log] _updateDiffContent: streaming diff.changes.length=${diff?.changes?.length}`);
			if (!diff || diff.identical || !diff.changes || diff.changes.length === 0) {
				this.logService.info('[KwaiPilot] _updateDiffContent: No differences found in streaming update');
				return;
			}
			const decorationsData = this.createDiffDecorations(diff, editor);
			this.logService.info(`[KwaiPilot][Log][DiffDecorations] BEFORE decorations.set: editor.getModel()?.uri=${editor.getModel()?.uri.toString()}, lineCount=${editor.getModel()?.getLineCount()}`);
			streamingState.decorations.set(decorationsData);
			this.logService.info(`[KwaiPilot][Log][DiffDecorations] AFTER decorations.set: editor.getModel()?.uri=${editor.getModel()?.uri.toString()}, lineCount=${editor.getModel()?.getLineCount()}`);
			editor.changeViewZones((viewZoneChangeAccessor: { removeZone: (id: string) => void; addZone: (zone: IViewZone) => string }) => {
				for (const id of streamingState.viewZones) {
					viewZoneChangeAccessor.removeZone(id);
				}
				streamingState.viewZones.length = 0;
			});
			this.renderDiffViewZones(diff, editor, streamingState.originalModel, streamingState.modifiedModel, streamingState.viewZones, state.isFinal);
			this.logService.info(`[KwaiPilot] _updateDiffContent: viewZones.length=${streamingState.viewZones.length}`);
		}
	}

	/**
	 * 渲染 diff 的 view zone（支持流式和一次性 diff）
	 */
	private renderDiffViewZones(
		diff: IDocumentDiff,
		editor: ICodeEditor,
		originalModel: ITextModel,
		modifiedModel: ITextModel,
		viewZones: string[],
		isFinal: boolean = false
	) {
		this.logService.info(`[KwaiPilot][Log][renderDiffViewZones] ENTER diff.changes.length=${diff.changes.length}, viewZones.length=${viewZones.length}, isFinal=${isFinal}`);
		const model = editor.getModel();
		this.logService.info(`[KwaiPilot][Log][renderDiffViewZones] editor.model.uri=${model?.uri.toString()}, lineCount=${model?.getLineCount()}`);
		if (isFinal) {
			this.removeChunkButtons();
			this.floatingChunkWidgets = [];
		}

		editor.changeViewZones(viewZoneChangeAccessor => {
			this.logService.info(`[KwaiPilot][Log][renderDiffViewZones] begin, diff.changes.length=${diff.changes.length}`);
			for (const id of viewZones) {
				viewZoneChangeAccessor.removeZone(id);
			}
			viewZones.length = 0;
			const mightContainNonBasicASCII = originalModel.mightContainNonBasicASCII();
			const mightContainRTL = originalModel.mightContainRTL();
			const renderOptions = RenderOptions.fromEditor(editor);
			const sortedChanges = [...diff.changes].sort((a, b) =>
				a.modified.startLineNumber - b.modified.startLineNumber
			);
			for (const [idx, diffEntry] of sortedChanges.entries()) {
				this.logService.info(`[KwaiPilot][Debug] render zone: idx=${idx}, original=[${diffEntry.original.startLineNumber},${diffEntry.original.endLineNumberExclusive}), modified=[${diffEntry.modified.startLineNumber},${diffEntry.modified.endLineNumberExclusive}), afterLine=${diffEntry.modified.startLineNumber - 1}`);
				try {
					let domNode: HTMLDivElement | undefined = undefined;
					let afterLine: number;
					let viewZoneId: number | undefined = undefined;
					const isInsert = diffEntry.modified && !diffEntry.modified.isEmpty && diffEntry.original && diffEntry.original.isEmpty;
					const chunkId = `diff-chunk-${idx}`;
					if (!diffEntry.original.isEmpty) {
						const source = new LineSource(
							diffEntry.original.mapToLineArray((l: any) => originalModel.tokenization.getLineTokens(l)),
							[],
							mightContainNonBasicASCII,
							mightContainRTL,
						);
						domNode = document.createElement('div');
						domNode.classList.add('chat-editing-original-zone', 'view-lines', 'line-delete', 'monaco-mouse-cursor-text');
						const result = renderLines(source, renderOptions, [], domNode);
						afterLine = Math.max(0, diffEntry.modified.startLineNumber - 1);
						this.logService.info(`[KwaiPilot][Debug] delete zone domNode text: ${domNode.textContent}`);

						const viewZoneData: IViewZone = {
							afterLineNumber: afterLine,
							heightInLines: result.heightInLines || 1,
							domNode,
							ordinal: 50000 + idx * 2
						};
						// 在第几行后添加空行，添加几行
						viewZoneId = viewZones.push(viewZoneChangeAccessor.addZone(viewZoneData));
						this.logService.info(`[KwaiPilot][Log] renderDiffViewZones: [${idx}] Add original (deleted/replaced) view zone after line ${afterLine}, heightInLines=${result.heightInLines}, class=${domNode.className}`);
					} else if (isInsert) {
						domNode = document.createElement('div');
						domNode.classList.add('kwaipilot-diff-insert-zone');
						const lines = [];
						for (let l = diffEntry.modified.startLineNumber; l < diffEntry.modified.endLineNumberExclusive; l++) {
							lines.push(modifiedModel.getLineContent(l));
						}
						const pre = document.createElement('pre');
						pre.textContent = lines.join('\n');
						pre.classList.add('kwaipilot-diff-insert-lines');
						domNode.appendChild(pre);
					}
					if (isFinal) {
						// contentWidget 按钮
						const chunkBtns = this.renderChunkButtons(
							idx,
							editor,
							() => {
								// accept chunk
								this.logService.info(`[KwaiPilot][Log] renderDiffViewZones: [${idx}] Accept chunk ${chunkId}`);
								if (!model) return;
								this.setChunkAccepted(model.uri, chunkId, true);
							},
							() => {
								// reject chunk
								this.logService.info(`[KwaiPilot][Log] renderDiffViewZones: [${idx}] Reject chunk ${chunkId}`);
								if (!model) return;
								this.setChunkRejected(model.uri, chunkId, false);
							}
						);
						chunkBtns.classList.add('kwaipilot-diff-chunk-btns-floating');
						const widgetId = `kwaipilot-diff-chunk-widget-${idx}`;
						/**
						 * 注意：超过了当前 model 的最大行数，Monaco 会自动将 widget 定位到最后一行的下方
						 * （即 maxLineNumber + 1 其实就是最后一行的下方），但视觉上会"吸附"在最后一行。
						 */
						const widget: IContentWidget = {
							getId: () => widgetId,
							getDomNode: () => chunkBtns,
							getPosition: () => ({
								position: { lineNumber: diffEntry.modified.endLineNumberExclusive, column: 1 },
								preference: [ContentWidgetPositionPreference.EXACT]
							}),
							allowEditorOverflow: true
						};
						editor.addContentWidget(widget);
						this.logService.info(`[KwaiPilot][Log] renderDiffViewZones: [${idx}] Add floating chunk widget after line ${diffEntry.modified.endLineNumberExclusive}, class=${chunkBtns.className}, widgetId=${widgetId}`);
						this.floatingChunkWidgets.push({
							id: widgetId,
							widget,
							chunkId,
							diffEntry,
							domNode: chunkBtns,
							viewZoneId
						});
						domNode?.appendChild(document.createComment('floating chunk btns moved to content widget'));
					}

				} catch (e) {
					this.logService.error(`[KwaiPilot][Log] renderDiffViewZones: [${idx}] Error rendering view zone: ${e}`);
				}
			}
			this.logService.info(`[KwaiPilot][Log] renderDiffViewZones: end, viewZones count = ${viewZones.length}`);
		});
		this.logService.info(`[KwaiPilot][Log][renderDiffViewZones] EXIT viewZones.length = ${viewZones.length}`);
		// 监听滚动和布局变化，动态更新按钮位置
		const updateBtnPositions = () => {
			this.logService.info(`[KwaiPilot][Log][renderDiffViewZones] updateBtnPositions: ENTER, viewZones.length = ${viewZones.length}`);
			const sortedChanges = [...diff.changes].sort((a, b) =>
				a.modified.startLineNumber - b.modified.startLineNumber
			);
			for (let i = 0; i < sortedChanges.length; i++) {
				if (this.floatingChunkWidgets[i]) {
					editor.layoutContentWidget(this.floatingChunkWidgets[i].widget);
				}
			}
			const rightOffset = this.getRightOffset(editor);

			this.logService.info(`[KwaiPilot][Log][renderDiffViewZones] updateBtnPositions: floatingChunkWidgets.length = ${this.floatingChunkWidgets.length}`);
			for (let i = 0; i < this.floatingChunkWidgets.length; i++) {
				const widgetObj = this.floatingChunkWidgets[i];
				if (widgetObj) {
					const domNode = widgetObj.widget.getDomNode() as HTMLDivElement;
					if (domNode) {
						domNode.style.right = `${rightOffset}px`;
						domNode.style.left = 'auto';
						domNode.style.position = 'absolute';
					}
					editor.layoutContentWidget(widgetObj.widget);
				}
			}
		};

		if (isFinal) {
			// 卸载旧的监听，避免重复注册
			if (this.scrollAndLayoutListeners) {
				this.scrollAndLayoutListeners.dispose();
			}
			this.scrollAndLayoutListeners = new DisposableStore();
			if (editor.onDidScrollChange) {
				this.scrollAndLayoutListeners.add(editor.onDidScrollChange(updateBtnPositions));
			}
			if (editor.onDidLayoutChange) {
				this.scrollAndLayoutListeners.add(editor.onDidLayoutChange(updateBtnPositions));
			}
		}
		this.logService.info(`[KwaiPilot][Log][renderDiffViewZones] EXIT viewZones.length = ${viewZones.length}`);
	}

	/** 获取代码块 accept / reject 按钮距离右侧的距离 */
	private getRightOffset(editor: ICodeEditor): number {
		const editorDom = editor.getDomNode();
		if (!editorDom) {
			return 20; // 默认偏移量
		}
		// 获取 minimap
		const minimap = editorDom.querySelector('.minimap');
		let minimapWidth = 0;
		if (minimap) {
			const rect = minimap.getBoundingClientRect();
			minimapWidth = rect.width || 0;
		}
		const scrollbar = editorDom.querySelector('.scrollbar.vertical .slider');
		let scrollbarWidth = 0;
		if (scrollbar) {
			const rect = scrollbar.getBoundingClientRect();
			scrollbarWidth = rect.width || 0;
		}
		// 计算右侧偏移量
		const rightOffset = (minimapWidth + scrollbarWidth) > 0 ? (minimapWidth + scrollbarWidth + 20) : 20;

		this.logService.info(`[KwaiPilot][Log][renderDiffViewZones] updateBtnPositions: minimapWidth = ${minimapWidth}, scrollbarWidth = ${scrollbarWidth}, rightOffset = ${rightOffset}`);

		return rightOffset;
	}

	/**
	 * 渲染 diff chunk 的 accept/reject 按钮
	 */
	private renderChunkButtons(idx: number, editor: ICodeEditor, onAccept: () => void, onReject: () => void): HTMLDivElement {
		this.logService.info(`[KwaiPilot][Log][renderChunkButtons] ENTER, idx = ${idx}`);
		const chunkBtnContainer = document.createElement('div');
		chunkBtnContainer.classList.add('kwaipilot-diff-chunk-btns');
		const rightOffset = this.getRightOffset(editor);
		chunkBtnContainer.style.right = `${rightOffset}px`;
		const acceptBtn = document.createElement('button');
		// label
		const acceptLabel = document.createElement('span');
		acceptLabel.textContent = 'Accept';
		acceptBtn.appendChild(acceptLabel);
		// icon
		const acceptIcon = document.createElement('span');
		acceptIcon.classList.add('shortcut-text'); // VS Code accept icon
		// allow-any-unicode-next-line
		acceptIcon.textContent = '⌘Y';
		acceptBtn.appendChild(acceptIcon);
		acceptBtn.classList.add('kwaipilot-diff-chunk-accept-btn');

		const rejectBtn = document.createElement('button');
		// label
		const rejectLabel = document.createElement('span');
		rejectLabel.textContent = 'Reject';
		rejectBtn.appendChild(rejectLabel);
		// icon
		const rejectIcon = document.createElement('span');
		rejectIcon.classList.add('shortcut-text'); // VS Code reject icon
		// allow-any-unicode-next-line
		rejectIcon.textContent = '⌘N';
		rejectBtn.appendChild(rejectIcon);
		rejectBtn.classList.add('kwaipilot-diff-chunk-reject-btn');
		acceptBtn.onclick = onAccept;
		rejectBtn.onclick = onReject;
		chunkBtnContainer.appendChild(acceptBtn);
		chunkBtnContainer.appendChild(rejectBtn);

		return chunkBtnContainer;
	}

	/** 文件来回切换&文件内容没有改变时，更新 */
	private async updateDiffDecorations(uri: URI) {
		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		if (!editor) {
			this.logService.info('[KwaiPilot][Log][updateDiffFooter] No active editor');
			this.removeDiffFooter();
			this.removeChunkButtons();
			return;
		}
		if (this.isStreaming) {
			this.logService.info('[KwaiPilot][Log][updateDiffFooter] Streaming mask detected, skip decorations/view zones restore');
			return;
		}
		if (this.isRestoring) {
			this.logService.info('[KwaiPilot][Log][updateDiffDecorations] isRestoring=true, skip update');
			return;
		}

		try {
			const diffAreas = this.getDiffAreas(uri);
			const allDiffAreas = Array.from(this.diffAreas.values());
			this.logService.info(`[KwaiPilot][Log][updateDiffFooter] uri = ${uri.toString()}, diffAreas.length = ${diffAreas?.length}, allDiffAreas.length = ${allDiffAreas.length}`);
			if ((!diffAreas || diffAreas.length === 0) && allDiffAreas.length > 0) {
				this.logService.info('[KwaiPilot][Log][updateDiffFooter] No diffAreas for this file, but other diffAreas exist, rendering review next file footer');
				this.renderReviewNextFileFooter(editor, uri, allDiffAreas);
				return;
			}
			if (!diffAreas || diffAreas.length === 0) {
				this.logService.info('[KwaiPilot][Log][updateDiffFooter] No diffAreas for this file, removing diff footer');
				this.removeDiffFooter();
				this.removeChunkButtons();
				return;
			}
			this.logService.info(`[KwaiPilot][Log][updateDiffFooter] Rendering diff footer for uri = ${uri.toString()}`);
			const diffArea = diffAreas[0];
			const model = editor.getModel();
			if (!model) return;

			if (!model || model.uri.toString() !== uri.toString()) {
				this.logService.info(`[KwaiPilot][Log][updateDiffDecorations] editor model uri mismatch, skip: model.uri = ${model?.uri.toString()}, target = ${uri.toString()} `);
				return;
			}

			// // 1. 彻底清空 decorations，防止叠加
			// model.deltaDecorations(model.getAllDecorations().map(d => d.id), []);
			// // 1.1 清空 view zone，防止叠加
			// this.renderDiffViewZones({ changes: [] }, editor, model, model, [], this.logService);

			let streamingState = this.streamingStates.get(uri.path.toString());
			const initialContent = streamingState?.initialContent ?? diffArea.diffs[0]?.content ?? '';
			const modelContent = model.getValue();

			this.logService.info(`[KwaiPilot][Log][updateDiffFooter] initialContent = ${initialContent}, modelContent = ${modelContent} `);

			// 更新当前流式状态
			this._currentStreamingState = {
				uri,
				content: modelContent,
				initialContent,
				isFinal: true
			};

			// 获取或初始化 streaming state
			if (!streamingState) {
				streamingState = await this.initStreamingState({
					uri,
					initialContent,
					currentContent: modelContent,
				});
				if (!streamingState) {
					return;
				}
				this.streamingStates.set(uri.path.toString(), streamingState);
			}


			this.isRestoring = true; // <--- 关键：异步流程开始时加锁，防止多次调用导致状态混乱
			// 直接更新 diff 内容
			await this._updateDiffContent(this._currentStreamingState, false);
		} finally {
			this.isRestoring = false; // <--- 关键：异步流程结束后再解锁
		}
	}

	/** 更新 diff footer */
	private async updateDiffFooter(uri: URI) {
		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		if (!editor) {
			this.logService.info('[KwaiPilot][Log][updateDiffFooter] No active editor');
			this.removeDiffFooter();
			this.removeChunkButtons();
			return;
		}
		if (this.isStreaming) {
			this.logService.info('[KwaiPilot][Log][updateDiffFooter] Streaming mask detected, skip decorations/view zones restore');
			return;
		}
		const diffAreas = this.getDiffAreas(uri);
		const allDiffAreas = Array.from(this.diffAreas.values());
		this.logService.info(`[KwaiPilot][Log][updateDiffFooter] uri = ${uri.toString()}, diffAreas.length = ${diffAreas?.length}, allDiffAreas.length = ${allDiffAreas.length} `);
		if ((!diffAreas || diffAreas.length === 0) && allDiffAreas.length > 0) {
			this.logService.info('[KwaiPilot][Log][updateDiffFooter] No diffAreas for this file, but other diffAreas exist, rendering review next file footer');
			this.renderReviewNextFileFooter(editor, uri, allDiffAreas);
			this.removeChunkButtons();
			return;
		}
		if (!diffAreas || diffAreas.length === 0) {
			this.logService.info('[KwaiPilot][Log][updateDiffFooter] No diffAreas for this file, removing diff footer');
			this.removeDiffFooter();
			this.removeChunkButtons();
			return;
		}
		this.renderDiffFooter(editor, uri, diffAreas);
	}

	/** 没有代码块 diff 时，渲染  Review next file，跳转到下一个打开的文件 */
	private renderReviewNextFileFooter(editor: ICodeEditor, uri: URI, allDiffAreas: DiffArea[]) {
		this.removeDiffFooter();
		const container = editor.getContainerDomNode();
		const footer = document.createElement('div');
		footer.classList.add('kwaipilot-review-next-file-footer');

		const reviewBtn = document.createElement('button');
		reviewBtn.classList.add('review-next-file-btn');
		const label = document.createElement('span');
		label.textContent = 'Review next file';
		reviewBtn.appendChild(label);
		const icon = document.createElement('span');
		icon.classList.add('codicon', 'codicon-drop-down-button');
		reviewBtn.appendChild(icon);
		reviewBtn.onclick = () => {
			const uris = Array.from(new Set(allDiffAreas.map(area => area.uri.toString())));
			const nextUri = uris.find(u => u !== uri.toString());
			if (nextUri) {
				this.editorService.openEditor({ resource: URI.parse(nextUri) });
			}
		};
		footer.appendChild(reviewBtn);
		container.appendChild(footer);
		this.diffFooterDom = footer;
		this.currentDiffIndex = 0;
	}


	private removeChunkButtons() {
		this.logService.info('[KwaiPilot][Log] removeChunkButtons: Removing all floating chunk buttons');
		// 清空 diff chunks
		this.floatingChunkWidgets.forEach(({ widget }) => {
			const editor = this.editorService.activeTextEditorControl as ICodeEditor;
			if (editor) {
				editor.removeContentWidget(widget);
			}
		});
		this.floatingChunkWidgets = [];
		if (this.scrollAndLayoutListeners) {
			this.scrollAndLayoutListeners.dispose();
			this.scrollAndLayoutListeners = undefined;
		}
	}



	/** 快捷键绑定 */
	private addKeybindings(editor: ICodeEditor, uri: URI) {
		this.removeFooterKeybindings();
		this._keybindingDisposables = new DisposableStore();
		const handler = (e: KeyboardEvent) => {
			if (e.metaKey && !e.shiftKey && !e.altKey && !e.ctrlKey) {
				if (e.key === 'Backspace') {
					e.preventDefault();
					e.stopPropagation();
					this.rejectDiff({ uri });
				} else if (e.key === 'Enter') {
					e.preventDefault();
					e.stopPropagation();
					this.acceptDiff({ uri });
				} else if (e.key === 'y' || e.key === 'Y') {
					e.preventDefault();
					e.stopPropagation();
					this.acceptCurrentChunk(editor, uri);
				} else if (e.key === 'n' || e.key === 'N') {
					e.preventDefault();
					e.stopPropagation();
					this.rejectCurrentChunk(editor, uri);
				}
			} else if (e.altKey && !e.metaKey && !e.shiftKey && !e.ctrlKey) {
				const footer = this.diffFooterDom;
				if (e.code === 'KeyN') {
					e.preventDefault();
					e.stopPropagation();
					const diffAreas = this.getDiffAreas(uri);
					if (!diffAreas || diffAreas.length === 0) return;
					const prevIndex = this.currentDiffIndex - 1;
					this.currentDiffIndex = prevIndex < 0 ? diffAreas[0].diffs.length - 1 : prevIndex;
					this.scrollToDiff(editor, diffAreas, this.currentDiffIndex);
					if (!footer) return;
					this.updateFooterIndex(footer, diffAreas);
				}
				if (e.code === 'KeyM') {
					e.preventDefault();
					e.stopPropagation();
					const diffAreas = this.getDiffAreas(uri);
					if (!diffAreas || diffAreas.length === 0) return;
					const total = diffAreas[0].diffs.length;
					const nextIndex = this.currentDiffIndex + 1;
					this.currentDiffIndex = nextIndex >= total ? 0 : nextIndex;
					this.scrollToDiff(editor, diffAreas, this.currentDiffIndex);
					if (!footer) return;
					this.updateFooterIndex(footer, diffAreas);
				}
			}
		};
		editor.getDomNode()?.addEventListener('keydown', handler, true);
		this._keybindingDisposables.add({ dispose: () => editor.getDomNode()?.removeEventListener('keydown', handler, true) });
	}

	/** 快捷键 accept 代码块 */
	private acceptCurrentChunk(editor: ICodeEditor, uri: URI) {
		const chunk = this.floatingChunkWidgets[this.currentDiffIndex];
		if (chunk) {
			this.setChunkAccepted(uri, chunk.chunkId, true);
		}
	}

	/** 快捷键 reject 代码块 */
	private rejectCurrentChunk(editor: ICodeEditor, uri: URI) {
		const chunk = this.floatingChunkWidgets[this.currentDiffIndex];
		if (chunk) {
			this.setChunkRejected(uri, chunk.chunkId, true);
		}
	}


	/** 移除快捷键绑定 */
	private removeFooterKeybindings() {
		if (this._keybindingDisposables) {
			this._keybindingDisposables.dispose();
			this._keybindingDisposables = undefined;
		}
	}

	/** 减少 footer 的total */
	private updateDiffFooterAfterChunkAcceptOrReject(uri: URI) {
		const footer = this.diffFooterDom;
		if (!footer) return;
		const diffAreas = this.getDiffAreas(uri);
		if (!diffAreas || diffAreas.length === 0) return;
		this.updateFooterIndex(footer, diffAreas);
	}

	/** 修改 renderDiffFooter，挂载/卸载文件级快捷键 */
	private renderDiffFooter(editor: ICodeEditor, uri: URI, diffAreas: DiffArea[]) {
		this.removeDiffFooter();
		this.addKeybindings(editor, uri);

		// 添加滚动监听，更新当前的 currentDiffIndex
		this.scrollAndLayoutListeners = new DisposableStore();
		this.scrollAndLayoutListeners.add(editor.onDidScrollChange(() => {
			if (this.isProgrammaticScroll) return;
			const visibleRanges = editor.getVisibleRanges();
			if (visibleRanges.length === 0) return;

			const visibleCenter = (visibleRanges[0].startLineNumber + visibleRanges[0].endLineNumber) / 2;
			let closestDiffIndex = 0;
			let minDistance = Infinity;

			this.floatingChunkWidgets.forEach((widget, index) => {
				const widgetLine = widget.diffEntry.modified.startLineNumber;
				const distance = Math.abs(widgetLine - visibleCenter);
				if (distance < minDistance) {
					minDistance = distance;
					closestDiffIndex = index;
				}
			});

			if (this.currentDiffIndex !== closestDiffIndex) {
				this.currentDiffIndex = closestDiffIndex;
				const footer = this.diffFooterDom;
				if (footer) {
					this.updateFooterIndex(footer, this.getDiffAreas(uri));
				}
			}
		}));

		const container = editor.getContainerDomNode();

		const footer = document.createElement('div');
		footer.classList.add('kwaipilot-diff-footer');

		const prevAndNextContainer = document.createElement('div');
		prevAndNextContainer.classList.add('prev-next-container');

		const prevBtn = document.createElement('button');
		prevBtn.classList.add('prev-diff-btn');
		const prevBtnIcon = document.createElement('span');
		prevBtnIcon.classList.add('codicon', 'codicon-drop-down-button');
		prevBtnIcon.onclick = () => {
			// 可以循环
			const prevIndex = this.currentDiffIndex - 1;
			this.currentDiffIndex = prevIndex < 0 ? diffAreas[0].diffs.length - 1 : prevIndex;
			this.scrollToDiff(editor, diffAreas, this.currentDiffIndex);
			this.updateFooterIndex(footer, diffAreas);
		};
		prevBtn.appendChild(prevBtnIcon);
		prevAndNextContainer.appendChild(prevBtn);

		// diff 索引
		const indexSpan = document.createElement('span');
		indexSpan.classList.add('diff-index-label');
		const total = diffAreas[0]?.diffs.length || 0;
		indexSpan.textContent = `${this.currentDiffIndex + 1} of ${total} `;
		prevAndNextContainer.appendChild(indexSpan);

		// 下一个 diff 块
		// const nextBtn = this._register(new SimpleButton({
		// 	label: 'prev diff',
		// 	icon: findNextMatchIcon,
		// 	className: 'next-diff-btn',
		// 	onTrigger: () => {
		// 		const total = diffAreas[0].diffs.length;
		// 		this.currentDiffIndex = Math.min(total - 1, this.currentDiffIndex + 1);
		// 		this.scrollToDiff(editor, diffAreas, this.currentDiffIndex);
		// 		this.updateFooterIndex(footer, diffAreas);
		// 	}
		// }, this.hoverService));
		const nextBtn = document.createElement('button');
		nextBtn.classList.add('next-diff-btn');
		const nextBtnIcon = document.createElement('span');
		nextBtnIcon.classList.add('codicon', 'codicon-drop-down-button');
		nextBtnIcon.onclick = () => {
			const total = diffAreas[0].diffs.length;
			// 可以循环
			const nextIndex = this.currentDiffIndex + 1;
			this.currentDiffIndex = nextIndex >= total ? 0 : nextIndex;
			this.scrollToDiff(editor, diffAreas, this.currentDiffIndex);
			this.updateFooterIndex(footer, diffAreas);
		};
		nextBtn.appendChild(nextBtnIcon);
		prevAndNextContainer.appendChild(nextBtn);

		footer.appendChild(prevAndNextContainer);

		// Reject
		const rejectBtn = document.createElement('button');
		rejectBtn.classList.add('reject-file-btn');
		// label
		const rejectLabel = document.createElement('span');
		rejectLabel.textContent = 'Reject file';
		rejectBtn.appendChild(rejectLabel);
		rejectBtn.onclick = () => {
			this.logService.info('[KwaiPilot] Reject file clicked');
			this.rejectDiff({ uri });
			this.kwaiPilotBridgeAPIService.editor.undoDiff({ filepath: this.getRelativePathForFile(uri) });
		};
		// icon
		const rejectIcon = document.createElement('span');
		rejectIcon.classList.add('shortcut-text'); // VS Code discard icon
		// allow-any-unicode-next-line
		rejectIcon.textContent = '⌘⌫';
		rejectBtn.appendChild(rejectIcon);

		footer.appendChild(rejectBtn);

		// Accept
		const acceptBtn = document.createElement('button');
		acceptBtn.classList.add('accept-file-btn');
		// label
		const acceptLabel = document.createElement('span');
		acceptLabel.textContent = 'Accept file';
		acceptBtn.appendChild(acceptLabel);
		acceptBtn.onclick = () => {
			this.logService.info('[KwaiPilot] Accept file clicked');
			this.acceptDiff({ uri });
			this.kwaiPilotBridgeAPIService.editor.keepDiff({ filepath: this.getRelativePathForFile(uri) });
		};
		// icon
		const acceptIcon = document.createElement('span');
		acceptIcon.classList.add('shortcut-text'); // VS Code accept icon
		// allow-any-unicode-next-line
		acceptIcon.textContent = '⌘⏎';
		acceptBtn.appendChild(acceptIcon);
		footer.appendChild(acceptBtn);

		const uris = Array.from(new Set(Array.from(this.diffAreas.values()).map(area => area.uri.toString())));
		const isMultipleDiffFile = uris.length > 1;

		if (isMultipleDiffFile) {
			const prevFileBtn = document.createElement('button');
			prevFileBtn.classList.add('prev-file-btn');
			const prevFileIcon = document.createElement('span');
			prevFileIcon.classList.add('codicon', 'codicon-drop-down-button');
			prevFileBtn.appendChild(prevFileIcon);
			// allow-any-unicode-next-line
			prevFileBtn.title = '上一个 diff 文件';
			prevFileBtn.onclick = () => {
				this.navigateDiffFile(-1);
			};
			footer.appendChild(prevFileBtn);

			const nextFileBtn = document.createElement('button');
			nextFileBtn.classList.add('next-file-btn');
			const nextFileIcon = document.createElement('span');
			nextFileIcon.classList.add('codicon', 'codicon-drop-down-button');
			nextFileBtn.appendChild(nextFileIcon);
			// allow-any-unicode-next-line
			nextFileBtn.title = '下一个 diff 文件';
			nextFileBtn.onclick = () => {
				this.navigateDiffFile(1);
			};
			footer.appendChild(nextFileBtn);
		}

		// 插入到编辑器容器
		container.appendChild(footer);
		this.diffFooterDom = footer;
		this.currentDiffIndex = 0;
		this.updateFooterIndex(footer, diffAreas);

		// 初始滚动到第一个 diff
		this.scrollToDiff(editor, diffAreas, 0);
	}

	/** 导航到下一个/上一个 diff 文件 */
	private navigateDiffFile(direction: number) {
		// direction: -1 = prev, 1 = next
		const uris = Array.from(new Set(Array.from(this.diffAreas.values()).map(area => area.uri.toString())));
		if (!uris.length) return;
		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		const model = editor?.getModel();
		if (!model) return;

		// 精准匹配：比较完整的文件标识（scheme + authority + path + query + fragment）
		const currentUri = model.uri;
		let targetFileUri: URI | undefined;

		// 如果当前是 kwaipilot-diff-modified scheme，需要找到对应的原始文件
		if (currentUri.scheme === 'kwaipilot-diff-modified') {
			// 构造对应的 file:// URI 进行匹配
			targetFileUri = URI.file(currentUri.path);
		} else {
			targetFileUri = currentUri;
		}

		// 在 uris 中查找完全匹配的文件
		const curIdx = uris.findIndex(uri => {
			const parsedUri = URI.parse(uri);
			return parsedUri.toString() === targetFileUri!.toString();
		});

		if (curIdx === -1) return;
		let nextIdx = curIdx + direction;
		if (nextIdx < 0) nextIdx = uris.length - 1;
		if (nextIdx >= uris.length) nextIdx = 0;
		const nextUri = uris[nextIdx];
		// 打开下一个/上一个 diff 文件
		this.editorService.openEditor({ resource: URI.parse(nextUri) });
	}

	/** update footer 中目前的代码块索引 */
	private updateFooterIndex(footer: HTMLDivElement, diffAreas: DiffArea[]) {
		const indexSpan = footer.querySelector('.diff-index-label');
		if (indexSpan && diffAreas.length > 0) {
			const total = diffAreas[0].diffs.length;
			indexSpan.textContent = `${this.currentDiffIndex + 1} / ${total}`;
		}
	}

	private isSameNumber(num1: any, num2: any) {
		if (!num1 && !num2) return true;
		return num1 === num2;
	}

	private isSameDiffEntry(diffEntry1: DetailedLineRangeMapping, diffEntry2: DetailedLineRangeMapping): boolean {
		return this.isSameNumber(diffEntry1.modified.startLineNumber, diffEntry2.modified.startLineNumber) &&
			this.isSameNumber(diffEntry1.modified.endLineNumberExclusive, diffEntry2.modified.endLineNumberExclusive) &&
			this.isSameNumber(diffEntry1.original.startLineNumber, diffEntry2.original.startLineNumber) &&
			this.isSameNumber(diffEntry1.original.endLineNumberExclusive, diffEntry2.original.endLineNumberExclusive);

	}

	/** 滚动到指定 diff 行 */
	private scrollToDiff(editor: ICodeEditor, diffAreas: DiffArea[], idx: number) {
		if (!diffAreas.length) return;
		const diffs = diffAreas[0].diffs;
		if (!diffs || diffs.length === 0) return;
		const target = diffs[Math.max(0, Math.min(idx, diffs.length - 1))];
		this.isProgrammaticScroll = true;
		editor.revealLineInCenter(target.modifiedRange.startLineNumber);
		setTimeout(() => { this.isProgrammaticScroll = false; }, 0); // 微任务后解锁
	}

	private updateInitialContentAfterChunkAccept(uri: URI, chunkId: string) {
		const streamingState = this.streamingStates.get(uri.path.toString());
		if (!streamingState) return;
		const model = this.modelService.getModel(uri);
		if (!model) return;

		// 1. 先用 initialContent 作为基准
		const lines = streamingState.initialContent.split('\n');

		const chunkInfo = this.floatingChunkWidgets.find(widget => widget.chunkId === chunkId);
		const diffEntry = chunkInfo?.diffEntry;
		if (!chunkInfo || !diffEntry) {
			return;
		}
		const range = diffEntry.original; // {startLineNumber, endLineNumber}
		const modifiedRange = diffEntry.modified;
		if (!range || !modifiedRange) {
			return;
		}
		// 3. 用当前 model 的内容替换 initialContent 对应 chunk 区域
		const newLines = model.getValue().split('\n').slice(
			modifiedRange.startLineNumber - 1,
			modifiedRange.endLineNumberExclusive - 1
		);

		lines.splice(
			range.startLineNumber - 1,
			range.endLineNumberExclusive - range.startLineNumber,
			...newLines
		);

		// 4. 更新 initialContent
		streamingState.initialContent = lines.join('\n');
		if (this._currentStreamingState) {
			this._currentStreamingState = {
				...this._currentStreamingState,
				initialContent: streamingState.initialContent,
			};
		}
		this.logService.info(`[KwaiPilot][Log] updateInitialContentAfterChunkAccept: uri = ${uri.path.toString()}, initialContent = ${streamingState.initialContent}`);
		// 更新 streamingStates
		this.streamingStates.set(uri.path.toString(), streamingState);
	}

	/** 设置 chunk 接受状态 */
	private setChunkAccepted(uri: URI, chunkId: string, accepted: boolean) {
		const uriStr = uri.path.toString();

		this.logService.info(`[KwaiPilot][Log] setChunkAccepted: uri = ${uriStr}, chunkId = ${chunkId}, accepted = ${accepted}`);
		// 更新 initialContent
		this.updateInitialContentAfterChunkAccept(uri, chunkId);

		const editor = this.editorService.activeTextEditorControl as ICodeEditor;
		this.removeChunkDecoration(uri, editor, chunkId);


		if (this.isAllChunksHandled(uri)) {
			this.clearAllDiffState(uri);
		} else {
			// 关键：刷新 decorations 和 footer
			// TODO: 更新文件内容
			this.updateDiffDecorations(uri);
			this.updateDiffFooterAfterChunkAcceptOrReject(uri);
		}
	}

	/** 设置 chunk 拒绝状态 TODO： 这里 rejected 参数的意义是什么  */
	private async setChunkRejected(uri: URI, chunkId: string, rejected: boolean) {

		const editor = this.editorService.activeTextEditorControl as ICodeEditor;

		const newContent = await this.restoreChunkOriginalCode(editor, chunkId);

		this.removeChunkDecoration(uri, editor, chunkId);
		const streamingState = this.streamingStates.get(uri.path.toString());

		// 更新 streamingStates
		if (this._currentStreamingState && newContent !== undefined) {
			this._currentStreamingState = {
				...this._currentStreamingState!,
				content: newContent || '',
			};

		}

		if (streamingState && newContent !== undefined) {
			this.streamingStates.set(uri.path.toString(), {
				...streamingState,
				currentContent: newContent,
			});
		}

		// 关键：刷新 decorations 和 footer
		if (this.isAllChunksHandled(uri)) {
			this.clearAllDiffState(uri);
		} else {
			// 关键：刷新 decorations 和 footer
			// TODO: 更新文件内容
			this.updateDiffDecorations(uri);
			this.updateDiffFooterAfterChunkAcceptOrReject(uri);
		}
	}

	/**
	 * 恢复当前 chunk 的原始代码
	 * @param editor 当前编辑器
	 * @param chunkId chunk 的唯一标识
	 * @returns 新代码
	 */
	private async restoreChunkOriginalCode(editor: ICodeEditor, chunkId: string): Promise<string | undefined> {
		this.logService.info(`[KwaiPilot][Log] restoreChunkOriginalCode: chunkId = ${chunkId}`);
		const model = editor.getModel();
		if (!model) {
			this.logService.info('[KwaiPilot][Log] removeChunkDecoration: No model found in editor');
			return;
		}
		const state = this.streamingStates.get(model.uri.path.toString());
		if (!state) {
			return;
		}
		const chunkInfo = this.floatingChunkWidgets.find(widget => widget.chunkId === chunkId);
		const diffEntry = chunkInfo?.diffEntry;
		if (!chunkInfo || !diffEntry) {
			return;
		}

		// 1. 获取当前文件内容
		const currentLines = model.getValue().split('\n');

		// 2. 获取原始内容
		const originalRange = diffEntry.original;
		const originalLines = state.initialContent.split('\n').slice(
			originalRange.startLineNumber - 1,
			originalRange.endLineNumberExclusive - 1
		);

		// 3. 获取修改范围
		const modifiedRange = diffEntry.modified;

		// 4. 替换内容
		currentLines.splice(
			modifiedRange.startLineNumber - 1,
			modifiedRange.endLineNumberExclusive - modifiedRange.startLineNumber,
			...originalLines
		);

		const newContent = currentLines.join('\n');

		// 5. 直接写入文件
		try {
			await this.textFileService.write(model.uri, newContent, {
				writeElevated: false
			});
			this.logService.info('[KwaiPilot] restoreChunkOriginalCode: Successfully wrote file content');
			return newContent;
		} catch (error) {
			this.logService.error(`[KwaiPilot] restoreChunkOriginalCode: Failed to write file: ${error}`);
			return undefined;
		}
	}

	private removeChunkDecoration(uri: URI, editor: ICodeEditor, chunkId: string): void {
		this.logService.info(`[KwaiPilot][Log] removeChunkDecoration: chunkId = ${chunkId}, uri = ${uri.toString()}`);
		const model = editor.getModel();
		if (!model) {
			this.logService.info('[KwaiPilot][Log] removeChunkDecoration: No model found in editor');
			return;
		}
		const state = this.streamingStates.get(model.uri.path.toString());
		if (!state) {
			return;
		}
		const chunkInfo = this.floatingChunkWidgets.find(widget => widget.chunkId === chunkId);
		const diffEntry = chunkInfo?.diffEntry;
		if (!chunkInfo || !diffEntry) {
			return;
		}

		const diffAreas = this.getDiffAreas(uri);

		// 移除 diffAreas 里的 chunk
		if (diffAreas.length > 0) {
			const area = diffAreas[0];
			const idx = area.diffs.findIndex(d => this.isSameDiffEntry(d.change, diffEntry));
			if (idx !== -1) {
				area.diffs.splice(idx, 1);
				if (area.diffs.length === 0) {
					this.diffAreas.delete(area.id);
				}
			}
		}

		const rmDecoration = this.decorationCollection.find(d => this.isSameDiffEntry(d.diffEntry, diffEntry));

		// 1. 移除 model decoration
		if (rmDecoration?.decorationIds && rmDecoration.decorationIds.length) {
			model.deltaDecorations(rmDecoration?.decorationIds?.map(String), []);
			rmDecoration.decorationIds = [];
		}

		// 3. 移除 view zones
		if (chunkInfo.viewZoneId) {
			editor.changeViewZones(accessor => {
				accessor.removeZone(String(chunkInfo.viewZoneId));
			});
		}

		if (chunkInfo.widget) {
			editor.removeContentWidget(chunkInfo.widget);
		}

		if (chunkInfo.domNode && chunkInfo.domNode.parentElement) {
			chunkInfo.domNode.parentElement.removeChild(chunkInfo.domNode);
		}

		const idx = this.floatingChunkWidgets.findIndex(w => w.chunkId === chunkId);
		if (idx !== -1) {
			this.floatingChunkWidgets.splice(idx, 1);
		}

		// 4. 清理 gutter decorations等（如有）
		// ...同理处理...

		// 5. 可选：从 state.chunks 里移除该 chunk
		// delete state.chunks[chunkId];
	}


	/** 每一个 chunk 都被 accept/reject */
	private isAllChunksHandled(uri: URI): boolean {

		const chunks = this.floatingChunkWidgets;
		if (chunks.length === 0) {
			this.kwaiPilotBridgeAPIService.editor.undoDiff({ filepath: this.getRelativePathForFile(uri) });
			return true;
		}

		return false;

	}
	/** 清除指定 URI 的 chunk 接受状态 */
	private clearChunkAcceptStates(uri: URI) {
		this.chunkAcceptStates.delete(uri.path.toString());
		this.chunkRejectStates.delete(uri.path.toString());
	}

	// private removeDiffDecorations(editor: ICodeEditor) {
	// 	const model = editor.getModel();
	// 	if (!model) {
	// 		this.logService.info('[KwaiPilot][Log] removeDiffDecorations: No model found in editor');
	// 		return;
	// 	}
	// 	this.logService.info(`[KwaiPilot][Log] removeDiffDecorations: model.uri=${model.uri.toString()}, lineCount=${model.getLineCount()}`);
	// 	// 清除所有 diff 装饰器
	// 	const decorations = model.getAllDecorations();
	// 	const diffDecorations = decorations.filter(d => d.options.className && d.options.className.includes('kwaipilot-diff-decoration'));
	// 	if (diffDecorations.length > 0) {
	// 		this.logService.info(`[KwaiPilot][Log] removeDiffDecorations: Removing ${diffDecorations.length} diff decorations`);
	// 		model.deltaDecorations(diffDecorations.map(d => d.id), []);
	// 	} else {
	// 		this.logService.info('[KwaiPilot][Log] removeDiffDecorations: No diff decorations found');
	// 	}
	// }

	/** 移除 diff footer 菜单 */
	private removeDiffFooter() {
		this.logService.info('[KwaiPilot][Log] removeDiffFooter: Removing diff footer');
		if (this.diffFooterDom && this.diffFooterDom.parentElement) {
			this.diffFooterDom.parentElement.removeChild(this.diffFooterDom);
			this.diffFooterDom = undefined;
		}
		this.currentDiffIndex = 0;
	}

	/** 流式更新时禁止编辑 */
	private setStreamingMask(editor: ICodeEditor, show: boolean) {
		if (show) {
			editor.updateOptions({ readOnly: true });
		} else {
			editor.updateOptions({ readOnly: false });
		}
	}

	/**
 * 获取文件相对于工作区的路径
 * @param uri 文件URI
 * @returns 相对路径字符串，如果无法获取则返回完整路径
 */
	private getRelativePathForFile(uri: URI): string {
		const root = this.workspaceContextService.getWorkspaceFolder(uri);
		if (root) {
			// 直接使用工作区根目录计算相对路径，而不是父目录
			return getRelativePath(root.uri, uri) ?? uri.path;
		}
		// 如果没有工作区，返回文件路径
		return uri.path;
	}

}



// Register service
registerSingleton(IEditCodeService, EditCodeService, InstantiationType.Delayed);
