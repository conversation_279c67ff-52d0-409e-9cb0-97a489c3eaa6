/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-workbench .terminal-widget-container {
	position: absolute;
	left: 0;
	bottom: 0;
	right: 0;
	top: 0;
	overflow: visible;
}

.monaco-workbench .terminal-overlay-widget {
	position: absolute;
	left: 0;
	bottom: 0;
	color: #3794ff;
}

.monaco-workbench .terminal-hover-target {
	position: absolute;
	z-index: 33;
}

.monaco-workbench .terminal-env-var-info {
	position: absolute;
	right: 10px; /* room for scroll bar */
	top: 0;
	width: 28px;
	height: 28px;
	text-align: center;
	z-index: 32;
	opacity: 0.5;
}

.monaco-workbench .terminal-env-var-info:hover,
.monaco-workbench .terminal-env-var-info.requires-action {
	opacity: 1;
}

.monaco-workbench .terminal-env-var-info.codicon {
	line-height: 28px;
}

.monaco-workbench .terminal-selection-action-widget {
	align-items: center;
    border-radius: 4px;
    color: var(--vscode-foreground);
    display: flex;
    flex-direction: row;
    gap: 4px;
    text-align: right;
    width: fit-content;
	position: absolute;
    right: 12px;
    transition: opacity .2s;
    z-index: 100;
    opacity: 0;
    pointer-events: none;
}

.monaco-workbench .terminal-selection-action-widget.visible {
    opacity: 1;
    pointer-events: auto;
}

.monaco-workbench .terminal-selection-action-widget .add-to-chat-btn {
	cursor: pointer;
    align-items: center;
    background-color: var(--vscode-input-background);
    border: 1px solid var(--vscode-commandCenter-inactiveBorder);
    border-radius: 4px;
    box-shadow: 0 4px 8px var(--vscode-inlineChat-shadow);
    color: var(--vscode-foreground);
    flex-direction: row;
    font-size: 12px;
    justify-content: center;
    overflow: hidden;
    padding: 2px 4px;
    text-transform: underline;
    z-index: 1000;
}

.monaco-workbench .terminal-selection-action-widget .shortcut-hint {
    color: var(--vscode-input-placeholderForeground);
    font-size: 10px;
    margin-left: 4px;
}
