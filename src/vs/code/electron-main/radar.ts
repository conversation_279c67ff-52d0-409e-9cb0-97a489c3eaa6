import { createRequire } from 'node:module';
const require = createRequire(import.meta.url);
const RadarMain = require('@ks-radar/electron/main');
import { DisposableStore } from '../../base/common/lifecycle.js';
import { IStorageMain } from '../../platform/storage/electron-main/storageMain.js';
import { IStorageMainService } from '../../platform/storage/electron-main/storageMainService.js';

interface CustomDimension {
	c_dimension1?: any;
	c_dimension2?: any;
	c_dimension3?: any;
	release_tag?: any;
	product_version?: any;
	sub_app?: string;
}
interface RadarMainOptions {
	projectId: string;
	customDimensions?: CustomDimension;
	ignoreList?: string[];
	commonDimensions: {
		deviceId?: string;
		userId?: string;
		versionName?: string;
		product?: string;
	};
	customAxios?: any;
	gapTime?: number;
	logRecall?: any;
}


// 存储相关的常量（从loginUtils.ts中引用）
const DEVICE_ID_KEY = 'kwaipilot.deviceId';
const USER_INFO_KEY = 'kwaipilot.userInfo';

// 创建 RadarMain 实例的配置对象
const radarConfig: RadarMainOptions = {
	projectId: '0ecc330179', // 【必填】雷达项目ID
	commonDimensions: {
		// userId: 'zhangheng05', // 用户名
		// deviceId: 'xxxxx', // 不传则默认通过node-machine-id生成
		// versionName: '*********', // 不传则默认通过app.getVersion()取
		// product: 'kwaishop', // 不传则默认通过app.name获取
	},
	// 【选填】自定义公共维度，所有日志都会带上
	// customDimensions: {
	// 	c_dimension1: 'xx', // 【选填】自定义维度1
	// 	c_dimension1: 'xx', // 【选填】自定义维度2
	// 	c_dimension1: 'xx', // 【选填】自定义维度3
	// 	release_tag: '1.0.2', // 【选填】发布标签
	// 	product_version: '1.1.1', // 【选填】产品版本号
	// 	sub_app: 'test1' // 【选填】子应用
	// }
};

// @ts-ignore - RadarMain 来自外部依赖，类型定义可能不完整
const radarMain = new RadarMain(radarConfig);

// 上报主进程启动事件
radarMain.event({
	name: 'RADAR_MAIN_START', // 必填
});

// 上报插件进程启动的性能事件
// 记录插件进程启动时间，这是一个关键的性能指标

const startTimestamp = Date.now();
radarMain.event({
	name: 'PLUGIN_PROCESS_START', // 插件进程启动事件
	event_type: 'performance', // 性能事件类型
	category: 'startup', // 启动业务分类
	src: 'electron-main/radar.ts', // 事件源文件
	result_type: 'success', // 启动成功
	extra_info: {
		timestamp: startTimestamp,
		process_type: 'plugin_main'
	}
});

// 扩展主机相关性能事件上报计数器，确保不超过限制
let extensionHostEventCount = 0;
const MAX_EXTENSION_HOST_EVENTS = 2; // 最多上报2个扩展主机相关的事件

/**
 * 处理扩展主机相关的性能事件上报
 * @param eventName 事件名称
 * @param data 事件数据
 */
function reportExtensionHostStage(eventName: string, data: { timestamp: number }) {
	// 检查是否超过上报限制
	if (extensionHostEventCount >= MAX_EXTENSION_HOST_EVENTS) {
		console.warn(`Extension host event reporting limit reached. Skipping: ${eventName}`);
		return;
	}

	try {
		let radarEventName: string;

		switch (eventName) {
			case 'extensionHost.created':
				radarEventName = 'EXTENSION_HOST_CREATED';
				break;
			case 'extensionHost.started':
				radarEventName = 'EXTENSION_HOST_STARTED';
				break;
			default:
				console.warn(`Unknown extension host event: ${eventName}`);
				return;
		}

		radarMain.event({
			name: radarEventName,
			event_type: 'performance',
			category: 'extension_host',
			src: 'platform/extensions/electron-main/extensionHostStarter.ts',
			result_type: 'success',
			extra_info: {
				timestamp: data.timestamp,
				event_source: eventName,
				duration: data.timestamp - startTimestamp
			}
		});

		extensionHostEventCount++;
		console.log(`Radar: Reported extension host event ${radarEventName} (${extensionHostEventCount}/${MAX_EXTENSION_HOST_EVENTS})`);
	} catch (error) {
		console.warn(`Failed to report extension host event ${eventName}:`, error);
	}
}

/**
 * 初始化存储监听，用于监听用户信息变化并更新雷达维度
 * @param storageService 应用级存储服务
 */
function initializeStorageListener(storageService: IStorageMainService) {
	try {
		// allow-any-unicode-next-line
		console.log('Radar: 开始初始化存储监听器...');

		// 获取应用级存储实例
		const appStorage = storageService.applicationStorage;

		// 等待应用存储初始化完成后再进行操作
		appStorage.whenInit.then(() => {
			// allow-any-unicode-next-line
			console.log('Radar: 应用存储初始化完成，开始设置监听器');

			// 监听存储变化事件
			const disposables = new DisposableStore();

			// 监听所有存储变化，然后过滤我们关心的键
			const storageListener = appStorage.onDidChangeStorage((event) => {
				// 检查是否是我们关心的键
				if (event.key === USER_INFO_KEY) {
					// allow-any-unicode-next-line
					console.log('Radar: 检测到用户信息变化');
					updateRadarUserDimensions(appStorage);
				} else if (event.key === DEVICE_ID_KEY) {
					// allow-any-unicode-next-line
					console.log('Radar: 检测到设备ID变化');
					updateRadarDeviceDimensions(appStorage);
				}
			});

			// 将监听器添加到 disposables 中进行管理
			disposables.add(storageListener);

			// 初始化时读取并设置当前存储的用户信息
			updateRadarUserDimensions(appStorage);
			updateRadarDeviceDimensions(appStorage);

			// allow-any-unicode-next-line
			console.log('Radar: 存储监听器初始化成功');
		}).catch(error => {
			// allow-any-unicode-next-line
			console.error('Radar: 应用存储初始化失败:', error);
		});

	} catch (error) {
		// allow-any-unicode-next-line
		console.warn('Radar: 初始化存储监听器失败:', error);
	}
}

/**
 * 更新雷达的用户维度信息
 * @param appStorage 应用存储实例
 */
function updateRadarUserDimensions(appStorage: IStorageMain) {
	try {
		const userInfoStr = appStorage.get(USER_INFO_KEY);
		let userInfo = null;

		if (userInfoStr) {
			try {
				userInfo = JSON.parse(userInfoStr);
			} catch (error) {
				// allow-any-unicode-next-line
				console.warn('Radar: 解析用户信息失败:', error);
				return;
			}
		}

		// 获取设备ID
		const deviceId = appStorage.get(DEVICE_ID_KEY);

		// 更新雷达的公共维度
		radarMain.updateConfig({
			userId: userInfo?.name || undefined,
			deviceId: deviceId || undefined,
			// versionName 将由应用版本自动获取
			// product 将由应用名称自动获取
		});

		// 上报维度更新事件
		radarMain.event({
			name: 'RADAR_DIMENSIONS_UPDATED',
			event_type: 'business',
			category: 'user_management',
			src: 'electron-main/radar.ts',
			result_type: 'success',
			extra_info: {
				userId: userInfo?.name || 'anonymous',
				deviceId: deviceId || 'unknown',
				hasUserInfo: !!userInfo,
				timestamp: Date.now()
			}
		});

		// allow-any-unicode-next-line
		console.log('Radar: 用户维度更新完成:', {
			userId: userInfo?.name || 'anonymous',
			deviceId: deviceId || 'unknown'
		});
	} catch (error) {
		// allow-any-unicode-next-line
		console.warn('Radar: 更新用户维度失败:', error);
	}
}

/**
 * 更新雷达的设备维度信息
 * @param appStorage 应用存储实例
 */
function updateRadarDeviceDimensions(appStorage: IStorageMain) {
	try {
		const deviceId = appStorage.get(DEVICE_ID_KEY);

		if (deviceId) {
			// 获取用户信息以保持完整的维度更新
			const userInfoStr = appStorage.get(USER_INFO_KEY);
			let userInfo = null;

			if (userInfoStr) {
				try {
					userInfo = JSON.parse(userInfoStr);
				} catch (error) {
					// allow-any-unicode-next-line
					console.warn('Radar: 设备更新时解析用户信息失败:', error);
				}
			}

			// 更新雷达的公共维度
			radarMain.updateConfig({
				userId: userInfo?.name || undefined,
				deviceId: deviceId,
			});

			// allow-any-unicode-next-line
			console.log('Radar: 设备维度更新完成:', { deviceId });
		} else {
			// allow-any-unicode-next-line
			console.log('Radar: 未找到设备ID，跳过设备维度更新');
		}
	} catch (error) {
		// allow-any-unicode-next-line
		console.warn('Radar: 更新设备维度失败:', error);
	}
}

// 导出 radarMain 实例和相关功能，供其他模块使用
export { radarMain, reportExtensionHostStage, initializeStorageListener };
