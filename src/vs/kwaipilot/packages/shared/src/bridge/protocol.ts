import { ComposerState, InternalWebviewMessage, DiffSet } from "../agent";
import { SupportedModels } from "../agent/supportedModels";
import { DeleteMcpServerParams, McpServer, RestartMcpServerParams, ToggleMcpServerParams, InstallMcpParams, MCPFeaturedServer, FetchMcpDetailByMarketParams } from "../mcp/types";
import { FromIdeProtocol, ResponseBase } from "../LocalService";
import { MentionNodeV2Structure } from "../MentionNodeV2/nodes";
import { createProxyIdentifier } from "./proxyIdentifier";
import { SerializedDiagnostic } from "../misc/diagnostic";
import { ReportKeys } from "../misc/logger";
import { SettingPage } from "../customSettingPanel";
import { SerializedEditorState } from "lexical";
import { BridgeUploadFile } from "../misc/file";
import { StateReturnType } from "../state-manager/types";

/**
 * extension 端提供的助理模式相关 api
 */
export interface ExtensionComposerShape {
  $setEditingMessageTs(ts: number | undefined): void;

  /**
   * 恢复到指定的检查点
   * @param restoreCommitHash 检查点哈希值
   */
  $restoreCheckpoint(payload: {
    humanMessageTs: number;
    restoreCommitHash: string;
    updateStateImmediately: boolean;
  }): void;

  /**
   * 回退历史(不恢复检查点)
   * @param payload
   */
  $revertHistory(payload: {
    humanMessageTs: number;
    updateStateImmediately: boolean;
  }): void;
  /**
   * 获取diff
   * @param lhsHash 旧的检查点
   * @param rhsHash 新的检查点
   */
  $getDiffSet(lhsHash: string, rhsHash?: string): Promise<DiffSet[]>;

  /**
   * webview-ui 发起的 助理模式更新消息，例如用户输入、命令执行，具体查看
   * @see {InternalWebviewMessage} 了解更多信息。
   */
  $postMessageToComposerEngine(payload: InternalWebviewMessage): void;

  /**
   * 设置当前对话的模型
   * @param model @see {SupportedModels}
   */
  $setCurrentModel(model: SupportedModels): void;

  /** 切换 session 时， 切换历史记录 */
  $showTaskWithId(taskId: string): void;

  /**
   * 定位到 文件或 selection 等知识所属的文档
   * @param uri 文件 uri
   * @param range 范围
   */
  $locateMentionNodeV2(node: MentionNodeV2Structure): void;

  $locateByPath(absolutePath: string): void;

  $locateDiagnostic(uri: string, diagnostic: SerializedDiagnostic): void;

  /**
   * 唤起添加rules的流程
   */
  $addRuleFile(): void;

  /**
   * 打开设置页面并定位到 助理检测 lint 配置的章节
   */
  $openDiagnosticSetting(): void;

  /**
   * vscode.workspace.workspaceFile
   */
  $getWorkspaceFile(): string | undefined;
  /**
   * 设置当前工作区的 active session id
   * @param sessionId
   */
  $setActiveSessionId(sessionId: string): void;
  /**
   * 获取当前工作区的 active session id
   */
  $getActiveSessionId(): string;

  /**
 * 将用户输入的富文本结构体（editorState）转换为适合 LLM 的，带上下文的文本
 * @param editorState
 * @returns
 */
  $editorStateToContextualTextForLlm(editorState: SerializedEditorState, cwd: string, contextItems: MentionNodeV2Structure[]): Promise<string>;
}

/**
 * extension weblogger埋点
 */
export interface ExtensionWebloggerShape {
  $reportUserAction<T extends keyof ReportKeys>(param: {
    key: T;
    type?: string;
    chatId?: string;
    sessionId?: string;
    content?: string;
    operator?: string;
    subType?: string;
    applyId?: string;
  }): void;
}

/**
 * extension 索引构建
 */
export interface ExtensionIndexFileShape {
  $startBuildIndex(): void;
  $deleteIndex(): void;
  $stopIndex(): void;
  $rebuildIndex(): void;
  $openIndexIgnore(): void;
  $setMaxSpaceSize(value: number): void;
  $getMaxSpaceSize(): number;
  $getIsRepo(): boolean;
  $openSystemSettings(): void;
  $passThruToLocalService<T extends keyof FromIdeProtocol>(messageType: T, data: FromIdeProtocol[T][0]): Promise<FromIdeProtocol[T][1]>;
}

/**
 * extension MCP
 */
export interface ExtensionMCPShape {
  $passThruToLocalService<T extends keyof FromIdeProtocol>(messageType: T, data: FromIdeProtocol[T][0]): Promise<FromIdeProtocol[T][1]>;
  /** 获取完整 mcp 配置文件路径 */
  $getSettingsPath(): Promise<ResponseBase<string>>;
  /** 获取全量 mcp servers 列表 */
  $getAllMcpServers(): Promise<ResponseBase<{ mcpServers: McpServer[]; isError: boolean }>>;
  /** 启、停用单个 mcp server */
  $toggleMcpServer(params: ToggleMcpServerParams): Promise<ResponseBase<void>>;
  /** 重启单个 mcp server */
  $restartMcpServer(params: RestartMcpServerParams): Promise<ResponseBase<void>>;
  /** 删除单个 mcp server */
  $deleteMcpServer(params: DeleteMcpServerParams): Promise<ResponseBase<void>>;
  /** 获取 mcp 推荐服务器列表 */
  $getMcpFeaturedServers(): Promise<ResponseBase<{ records: MCPFeaturedServer[] }>>;
  /** 安装 mcp server */
  $installMcpServer(params: InstallMcpParams): Promise<ResponseBase<boolean>>;
  /** 获取 mcp 市场详情 */
  $fetchMcpDetailByMarket(params: FetchMcpDetailByMarketParams): Promise<ResponseBase<MCPFeaturedServer>>;
}
/**
 * extension Rules
 */
export interface ExtensionRulesShape {
  $openUserRule(): void;
  $openProjectRules(): void;
}

/**
 * Config 端提供的 api
 */
// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface ExtensionConfigShape {
}

/**
 * 开发者模式相关 api
 */
export interface ExtensionDeveloperShape {
  $reloadWebview(): void;
  $getIsDeveloperMode(): boolean;
  $setIsDeveloperMode(isDeveloperMode: boolean): void;
}
/**
 * webview 直接请求本地包的服务
 */
export interface ExtensionToLoaclShape {
  $parseRules(rules: string[]): Promise<string[]>;
}

/**
 * 设置页相关 api
 */
export interface ExtensionSettingsShape {
  $openSettings(activePage?: SettingPage): void;

  $updateSetting(key: keyof StateReturnType["config"], value: StateReturnType["config"][typeof key]): void;

  $getSettings(): StateReturnType["config"];
  /**
   * 登录
   * @param host ide | plugin 默认是 plugin
   */
  $login(host?: "ide" | "plugin"): void;
  /**
   * 登出
   * @param host ide | plugin 默认是 plugin
   */
  $logout(host?: "ide" | "plugin"): void;

  $importSettings(productName: string): void;
  $openIdeShortcutSettings(): void;
  $openIdeUserSettings(): void;
}

/**
 * 一些量级比较小或暂时不清楚归属的杂项 api
 */
export interface ExtensionMiscShape {
  $uploadFile(): Promise<BridgeUploadFile[]>;
}
/**
 * webview 端提供的助理模式相关 api
 */
export interface WebviewComposerShape {
  $postComposerStateUpdate(state: ComposerState): void;
  /**
   * 添加到上下文 如果不传，则只是定位到助理页面
   * @param node
   */
  $addToComposerContext(node: MentionNodeV2Structure | null): void;
}

/**
 * Extension 端提供的 api
 *
 * 注意：新增 api 时，需要同步更新 [bridge-registry](../../../../src/services/bridge-registry/index.ts) 中的注册
 */
export const ExtensionContext = {
  ExtensionComposer: createProxyIdentifier<ExtensionComposerShape>("ExtensionComposer"),
  ExtensionConfig: createProxyIdentifier<ExtensionConfigShape>("ExtensionConfig"),
  ExtensionIndexFile: createProxyIdentifier<ExtensionIndexFileShape>("ExtensionIndexFile"),
  ExtensionMCP: createProxyIdentifier<ExtensionMCPShape>("ExtensionMCP"),
  ExtensionRules: createProxyIdentifier<ExtensionRulesShape>("ExtensionRules"),
  ExtensionDeveloper: createProxyIdentifier<ExtensionDeveloperShape>("ExtensionDeveloper"),
  ExtensionSettings: createProxyIdentifier<ExtensionSettingsShape>("ExtensionSettings"),
  ExtensionToLoacl: createProxyIdentifier<ExtensionToLoaclShape>("ExtensionToLoacl"),
  ExtensionWeblogger: createProxyIdentifier<ExtensionWebloggerShape>("ExtensionWeblogger"),
  ExtensionMisc: createProxyIdentifier<ExtensionMiscShape>("ExtensionMisc"),
};

export const WebviewContext = {
  WebviewComposer: createProxyIdentifier<WebviewComposerShape>("WebviewComposer"),
};
