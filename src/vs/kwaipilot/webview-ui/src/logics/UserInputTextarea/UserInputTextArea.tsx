import { useCallback, useState, useMemo, useRef, useEffect, ComponentProps, ReactNode } from "react";
import { useBoolean, useColorMode, useMergeRefs } from "@chakra-ui/react";
import clsx from "clsx";

import {
  SharpCommand,
  // RichEditorBoxPanelData,
} from "@shared/types";
import { RichEditor } from "./RichEditor";
import { LexicalEditor, SerializedEditorState, SerializedLexicalNode } from "lexical";
import { Enable, Resizable } from "re-resizable";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { isMentionNode } from "shared/lib/MentionNode";
import { InternalLocalMessage, isHumanMessage } from "shared/lib/agent";
import { CustomScrollBar } from "@/components/CustomScrollbar";
import { ContextHeader } from "./ContextHeader";
import { ContextHeaderContext, ContextHeaderState, useContextHeaderState } from "./ContextHeader/ContextHeaderContext";
import { useEvent } from "react-use";
import { isMentionNodeV2, MentionNodeV2Structure, MentionNodeV2Structure_SlashCommand, slashCommandSetRequiringContextItem } from "shared/lib/MentionNodeV2/nodes";
import { isCustomVariableNode } from "shared/lib/CustomVariable";
import { useContextInitiation } from "./useContextInitiation";
import { EventBusEvents, useEventBusListener } from "@/utils/eventBus";
import { UserInputTextAreaContext } from "./UserInputTextAreaContext";

export interface UploadFile {
  biz?: string;
  filename: string;
  id?: number;
  uid: string;
  path?: string;
  url?: string;
  type?: string;
  size: number;
  username?: string;
  progress?: number;
  status?: "uploading" | "done" | "error";
  [key: string]: any;
}
export interface DocListType {
  id: number;
  name: string;
  urls?: string[];
}

export interface CommandPluginRef {
  close: () => void;
  open: () => void;
  getShown: () => boolean;
}

export interface UserInputTextareaProps extends ComponentProps<"div"> {
  editorClassName?: string;
  wrapperClassName?: string;
  enable?: Enable | false;
  /* 如果是正在编辑的某条消息 */
  localMessage?: InternalLocalMessage;
  role: "bottom" | "conversation";
  editorRef?: React.RefObject<LexicalEditor>;
  doSubmit: (state: {
    contextHeaderState: ContextHeaderState;
    editor: LexicalEditor;
  }) => Promise<{ result: boolean }>;
  doStop: () => void;
  isStreaming: boolean;
  sessionId: string;
  isContextConsumer: boolean;
  applyStatusElement?: ReactNode;
  /**
   * 输入框左下角附加区域
   */
  moreOpt?: ReactNode;
  /**
   * 发送按钮左侧位置
   */
  action?: ReactNode;
  placeholder: JSX.Element;
  uploadFileEnabled?: boolean;
  mode: "chat" | "composer";
  disabled?: boolean;
}

export const UserInputTextarea: React.FC<UserInputTextareaProps> = (props: UserInputTextareaProps) => {
  const {
    editorClassName,
    wrapperClassName,
    localMessage,
    role,
    editorRef: editorRefProp,
    doSubmit: _doSubmit,
    isStreaming,
    sessionId,
    isContextConsumer,
    applyStatusElement,
    moreOpt,
    placeholder,
    action: actionProp,
    uploadFileEnabled,
    mode,
    doStop,
    ...rest
  } = props;
  if (localMessage && !isHumanMessage(localMessage)) {
    throw new Error("data is not a human message");
  }

  const { colorMode: theme } = useColorMode();

  const contextHeaderState = useContextHeaderState({
    initialNodes: localMessage?.contextItems?.map(v => ({
      structure: v,
      followActiveEditor: false,
      isVirtualContext: false,
    })) || [],
    sessionId,
  });

  const initialEditorState = useMemo(() => localMessage?.editorState ? JSON.stringify(localMessage.editorState) : undefined, [localMessage]);

  const optRef = useRef<HTMLDivElement>(null);

  const editorRef = useRef<LexicalEditor>(null);
  const composedEditorRef = useMergeRefs(editorRef, editorRefProp);

  const clearRef = useRef<{
    clear: () => void;
  }>();
  const [richEditorState, setRichEditorState]
    = useState<SerializedEditorState<SerializedLexicalNode>>();
  const changeEditorState = useCallback(
    (state: SerializedEditorState<SerializedLexicalNode>) => {
      setRichEditorState(state);
    },
    [setRichEditorState],
  );

  const {
    resetContext,
    setEditorStateInitializationDone,
  } = useContextInitiation({
    /* 如果有正在编辑的历史消息, 优先使用历史消息 */
    isContextConsumer,
    contextHeaderState,
    disabledCurrentFileBinding: role !== "bottom",
    editor: editorRef,
    sessionId,
  });

  const resetEditorState = useCallback(() => {
    if (role === "bottom") {
      resetContext();
      clearRef.current?.clear();
    }
    else {
      setEditorStateInitializationDone(true);
    }
  }, [resetContext, role, setEditorStateInitializationDone]);

  useEffect(() => {
    resetEditorState();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sessionId]);

  const isDark = theme === "dark";

  const [focused, setFocused] = useState(false);

  const onResetCheckpoint: EventBusEvents["composer:onResetCheckpoint"] = useCallback(({ humanMessage }) => {
    const editor = editorRef.current;
    if (!editor) {
      return;
    }
    editor.setEditorState(editor.parseEditorState(humanMessage.editorState));
    if (humanMessage.contextItems.length) {
      contextHeaderState.setNodes(humanMessage.contextItems.map(v => ({
        structure: v,
        followActiveEditor: false,
        isVirtualContext: false,
      })));
    }
  }, [contextHeaderState]);

  useEventBusListener("composer:onResetCheckpoint", onResetCheckpoint);

  const isBlank = useMemo(() => {
    // 一些节点不支持单独出现
    const InvalidSingleMentionNodeType: MentionNodeV2Structure["type"][] = ["web", "knowledge", "codebase"];
    function walkHasText(node: SerializedLexicalNode): boolean {
      if (isCustomVariableNode(node)) {
        return true;
      }
      if (isMentionNode(node)) {
        return true;
      }
      else if (isMentionNodeV2(node) && !InvalidSingleMentionNodeType.includes(node.structure.type)) {
        return true;
      }

      else if ("text" in node) {
        return Boolean(String(node.text).trim());
      }
      if ("children" in node && Array.isArray(node.children)) {
        for (const child of node.children) {
          if (walkHasText(child)) {
            return true;
          }
        }
      }
      return false;
    }
    return richEditorState ? !walkHasText(richEditorState.root) : true;
  }, [richEditorState]);

  const slashCommandInvalid = useMemo<MentionNodeV2Structure_SlashCommand | undefined>(() => {
    function walkInvalid(node: SerializedLexicalNode): MentionNodeV2Structure_SlashCommand | undefined {
      if (isMentionNodeV2(node) && node.structure.type === "slashCommand") {
        const structure = node.structure;
        if (slashCommandSetRequiringContextItem.has(structure.command)) {
          // 检查是否没有contextItem，或者contextItem的uri为空
          if (!structure.contextItem || !structure.contextItem.uri || structure.contextItem.uri.trim() === "") {
            return structure;
          }
        }
      }

      if ("children" in node && Array.isArray(node.children)) {
        for (const child of node.children) {
          const invalidChild = walkInvalid(child);
          if (invalidChild) {
            return invalidChild;
          }
        }
      }
      return undefined;
    }
    return richEditorState ? walkInvalid(richEditorState.root) : undefined;
  }, [richEditorState]);

  const disabled = (!isStreaming && isBlank) || Boolean(slashCommandInvalid);

  const onEscape = useCallback(() => {
    if (localMessage) {
      kwaiPilotBridgeAPI.extensionComposer.$setEditingMessageTs(undefined);
    }
  }, [localMessage]);

  const doSubmit = useCallback(async () => {
    if (slashCommandInvalid) {
      kwaiPilotBridgeAPI.showToast({
        level: "info",
        message: `未选择 "/${slashCommandInvalid.label}" 指令指定的文件或代码`,
      });
    }
    if (disabled) {
      return;
    }
    if (isStreaming) {
      if (!isBlank) {
        kwaiPilotBridgeAPI.showToast({
          level: "error",
          message: "正在生成回答，请稍后尝试",
        });
      }
      return;
    }
    if (!editorRef.current) {
      return;
    }
    const res = await _doSubmit({
      contextHeaderState,
      editor: editorRef.current,
    });
    if (res.result) {
    /* 完成回答后清空 https://team.corp.kuaishou.com/task/B2489947 */
      resetEditorState();
    }
  }, [_doSubmit, contextHeaderState, disabled, isBlank, isStreaming, resetEditorState, slashCommandInvalid]);

  const dragRef = useRef<HTMLDivElement>(null);
  const { isDragging } = useDragFile(dragRef);
  const [, { on: onResize }] = useBoolean();

  const resizableRef = useRef<Resizable>(null);
  useEffect(() => {
    if (!props.enable) {
      // 如果关闭 resize 需要把 reziable 重新设置尺寸
      resizableRef.current?.updateSize({ });
    }
  });

  return (
    <ContextHeaderContext.Provider value={{ state: contextHeaderState }}>
      <UserInputTextAreaContext.Provider value={{ role }}>
        <div
          className={clsx(
            "flex relative flex-col",
            wrapperClassName,
          )}
          {...rest}
        >
          <div>
            {applyStatusElement}
            <Resizable
              ref={resizableRef}
              enable={props.enable ?? false}
              maxHeight={340}
              minHeight={116}
              onResize={onResize}
            >
              <div
                className={clsx("bg-input-background border border-dropdown-border hover:border-focusBorder flex flex-col rounded-lg transition-all relative h-full z-10 p-[1px]", focused && "border-focusBorder")}
                style={{
                  maxHeight: "340px",
                  boxShadow: !focused
                    ? ""
                    : isDark
                      ? "box-shadow: 0px 16px 30px 0px rgba(30, 147, 252, 0.08), 0px 8px 21.6px 0px rgba(36, 143, 253, 0.12), 0px 4px 4px 0px rgba(26, 150, 251, 0.08), 0px 2px 2px 0px rgba(24, 151, 251, 0.06)"
                      : "box-shadow: 0px 16px 30px 0px rgba(138, 192, 255, 0.08), 0px 8px 21.6px 0px rgba(138, 192, 255, 0.08), 0px 4px 4px 0px rgba(138, 192, 255, 0.08), 0px 2px 2px 0px rgba(138, 192, 255, 0.06)",
                }}
                ref={dragRef}
              >
                {editorRef.current && <ContextHeader zIndex={1} editor={editorRef.current} mode={mode} />}
                <CustomScrollBar className=" flex-auto">
                  <RichEditor
                    editorRef={composedEditorRef}
                    customOptions={{
                      sharpCommandEnabled: true,
                      uploadFileEnabled: uploadFileEnabled,
                      filterSharpCommandKeyList: [SharpCommand.CODEBASE, SharpCommand.FOLDER],
                    }}
                    initialEditorState={initialEditorState}
                    onSubmit={doSubmit}
                    onStop={doStop}
                    onEscape={onEscape}
                    optRef={optRef}
                    moreOpt={moreOpt}
                    action={actionProp}
                    changeEditorState={changeEditorState}
                    clearRef={clearRef}
                    disabled={disabled}
                    editorClassName={editorClassName}
                    className={clsx(
                      "flex-auto relative ",
                      {
                        "rounded-t-[7.5px] ": true,
                      },
                    )}
                    focused={focused}
                    onFocusedChange={setFocused}
                    loading={isStreaming}
                    placeholder={placeholder}
                    mode={mode}
                  />
                </CustomScrollBar>
                <div ref={optRef} className="rounded-b-[7px]"></div>
              </div>

              {isDragging && <div className=" absolute left-0 right-0 top-0 bottom-0 bg-[hsla(216,_28%,_14%,_0.7)] rounded-lg z-10"></div>}
            </Resizable>
          </div>
        </div>
      </UserInputTextAreaContext.Provider>
    </ContextHeaderContext.Provider>
  );
};

/**
 * TODO: 实现有问题
 * @param ref
 * @returns
 */
function useDragFile(ref: React.RefObject<HTMLElement>) {
  const [isDragging, setIsDragging] = useState(false);

  useEvent("dragover", (e: Event) => {
    const dragEvent = e as DragEvent;
    dragEvent.preventDefault();
    dragEvent.stopPropagation();
    setIsDragging(true);
  }, ref.current);

  useEvent("dragleave", (e: Event) => {
    const dragEvent = e as DragEvent;
    dragEvent.preventDefault();
    dragEvent.stopPropagation();
    setIsDragging(false);
  }, ref.current);

  useEvent("drop", (e: Event) => {
    const dragEvent = e as DragEvent;
    dragEvent.preventDefault();
    dragEvent.stopPropagation();
    setIsDragging(false);

    if (dragEvent.dataTransfer) {
      const items = dragEvent.dataTransfer.items;
      const files = dragEvent.dataTransfer.files;

      console.log("拖拽的文件列表:", Array.from(files));

      // 遍历拖拽的项目
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        if (item.kind === "file") {
          const file = item.getAsFile();
          if (file) {
            console.log("文件信息:", {
              name: file.name,
              type: file.type,
              size: file.size,
              lastModified: file.lastModified,
            });
          }
        }
      }
    }
  }, ref.current);

  return {
    isDragging,
  };
}
