# VSCode 测试流程文档

本文档详细介绍了 VSCode 项目的测试流程，包括不同类型的测试、执行方式和最佳实践。

## 🚀 快速开始

如果你是第一次运行测试，请按以下步骤操作：

```bash
# 1. 安装依赖
npm install

# 2. 编译项目
npm run compile

# 3. 下载 Electron
npm run electron

# 4. 运行基础单元测试
./scripts/test.sh --grep "basic"

# 5. 运行一个简单的集成测试
npm run test-extension -- -l vscode-colorize-tests
```

> 💡 **提示**：如果你只想快速验证代码更改，运行 `./scripts/test.sh --run path/to/your/test.ts` 即可。

## 目录

- [快速开始](#快速开始)
- [测试策略](#测试策略)
- [测试类型概览](#测试类型概览)
- [环境准备](#环境准备)
- [单元测试](#单元测试)
- [集成测试](#集成测试)
- [烟雾测试](#烟雾测试)
- [扩展测试](#扩展测试)
- [代码质量检查](#代码质量检查)
- [持续集成流程](#持续集成流程)
- [Linux 流水线配置](#linux-流水线配置)
- [调试和故障排除](#调试和故障排除)
- [最佳实践](#最佳实践)
- [快速参考](#快速参考)

## 测试策略

根据不同的开发阶段和需求，选择合适的测试策略：

### 🔧 开发阶段

| 场景 | 推荐测试 | 命令 | 时间 |
|------|----------|------|------|
| 修改单个函数 | 相关单元测试 | `./scripts/test.sh --run path/to/test.ts` | < 1分钟 |
| 修改模块接口 | 模块单元测试 + 相关集成测试 | `./scripts/test.sh --glob "**/module*.test.js"` | 2-5分钟 |
| 添加新功能 | 完整单元测试 | `./scripts/test.sh` | 5-15分钟 |
| UI 相关修改 | 单元测试 + 烟雾测试 | `./scripts/test.sh && npm run smoketest -- -f "UI"` | 10-30分钟 |

### 🚀 提交前检查

```bash
# 最小检查（快速）
npm run eslint && ./scripts/test.sh --grep "your-changes"

# 标准检查（推荐）
npm run precommit && npm run core-ci-pr

# 完整检查（重要功能）
npm run core-ci && ./scripts/test-integration.sh
```

### 🎯 发布前验证

```bash
# 完整测试套件
npm run core-ci
npm run extensions-ci
./scripts/test-integration.sh
npm run smoketest
```

## 测试类型概览

VSCode 项目包含以下几种类型的测试：

| 测试类型 | 描述 | 运行环境 | 执行时间 |
|---------|------|----------|----------|
| 单元测试 | 测试单个模块和函数 | Electron/Browser/Node.js | 快速 |
| 集成测试 | 测试模块间交互和 API | Electron/Web | 中等 |
| 烟雾测试 | 端到端 UI 自动化测试 | Electron/Web | 较慢 |
| 扩展测试 | 测试内置扩展功能 | Electron | 中等 |

## 环境准备

### 1. 基础环境要求

```bash
# Node.js 版本要求
node --version  # 推荐 v20.x

# 安装项目依赖
npm install

# 编译项目
npm run compile
```

### 2. 安装测试依赖

```bash
# 安装 Playwright（用于浏览器测试）
npm run playwright-install

# 安装 Electron（用于桌面测试）
npm run electron
```

### 3. 环境变量配置

```bash
# 设置崩溃报告目录
export VSCODECRASHDIR=$PWD/.build/crashes

# 设置日志目录
export VSCODELOGSDIR=$PWD/.build/logs

# 开启详细日志（可选）
export DEBUG=pw:browser  # Playwright 详细日志
export ELECTRON_ENABLE_LOGGING=1  # Electron 日志
```

## 单元测试

单元测试是最基础的测试类型，用于验证单个模块和函数的正确性。

### Electron 环境测试

```bash
# 运行所有单元测试
./scripts/test.sh

# 调试模式（显示窗口和开发者工具）
./scripts/test.sh --debug

# 运行特定测试文件
./scripts/test.sh --run src/vs/editor/test/browser/controller/cursor.test.ts

# 运行匹配模式的测试
./scripts/test.sh --glob "**/extHost*.test.js"

# 生成覆盖率报告
./scripts/test.sh --coverage

# 设置超时时间
./scripts/test.sh --timeout 10000
```

### 浏览器环境测试

```bash
# 在多个浏览器中运行测试
npm run test-browser -- --browser webkit --browser chromium

# 仅在 Chromium 中运行
npm run test-browser -- --browser chromium

# 运行特定测试
npm run test-browser -- --run src/vs/base/test/common/strings.test.ts
```

### Node.js 环境测试

```bash
# Node.js 环境单元测试
npm run test-node -- --run src/vs/editor/test/browser/controller/cursor.test.ts
```

### 单元测试最佳实践

1. **测试文件命名**：使用 `.test.ts` 后缀
2. **测试组织**：按模块结构组织测试文件
3. **测试覆盖率**：目标覆盖率 > 80%
4. **测试隔离**：每个测试应该独立运行

## 集成测试

集成测试验证不同模块之间的交互和 API 的正确性。

### Electron 环境集成测试

```bash
# 运行所有集成测试
./scripts/test-integration.sh

# 针对特定构建运行测试
INTEGRATION_TEST_ELECTRON_PATH="/path/to/vscode" ./scripts/test-integration.sh

# 运行特定的集成测试
./scripts/test-integration.sh --grep "API tests"
```

### Web 环境集成测试

```bash
# 在浏览器中运行集成测试
./scripts/test-web-integration.sh --browser chromium

# 调试模式
./scripts/test-web-integration.sh --browser chromium --debug

# 在 WebKit 中运行
./scripts/test-web-integration.sh --browser webkit
```

### 集成测试准备

```bash
# 编译集成测试
cd test/integration/browser
npm install
npm run compile
```

## 烟雾测试

烟雾测试是端到端的 UI 自动化测试，验证主要功能流程。

### 开发环境烟雾测试

```bash
# 基本烟雾测试
npm run smoketest

# Web 环境烟雾测试
npm run smoketest -- --web --browser chromium

# 详细日志模式
npm run smoketest -- --verbose

# 运行特定测试
npm run smoketest -- -f "Search"
```

### 生产构建烟雾测试

```bash
# 针对特定构建运行
npm run smoketest -- --build /Applications/Visual\ Studio\ Code\ -\ Insiders.app

# Web 构建测试
npm run smoketest -- --build /path/to/server-web-build --web --browser chromium

# 远程测试
npm run smoketest -- --build /path/to/vscode --remote
```

### 烟雾测试开发

```bash
# 监听模式开发
cd test/smoke
npm run watch
```

## 扩展测试

测试内置扩展的功能。

```bash
# 运行扩展测试
npm run test-extension

# 测试特定扩展
npm run test-extension -- -l vscode-colorize-tests
npm run test-extension -- -l markdown-language-features
npm run test-extension -- -l terminal-suggest
```

## 代码质量检查

### 静态代码分析

```bash
# ESLint 检查
npm run eslint

# StyleLint 检查
npm run stylelint

# TypeScript 编译检查
npm run monaco-compile-check
npm run tsec-compile-check
npm run vscode-dts-compile-check

# 代码层级检查
npm run valid-layers-check

# 属性初始化顺序检查
npm run property-init-order-check
```

### 代码格式化

```bash
# 代码卫生检查
npm run hygiene

# 预提交检查
npm run precommit
```

## 持续集成流程

### CI 核心流程

```bash
# 核心 CI 检查
npm run core-ci

# PR 核心检查
npm run core-ci-pr

# 扩展 CI 检查
npm run extensions-ci

# 扩展 PR 检查
npm run extensions-ci-pr
```

### 性能测试

```bash
# 性能基准测试
npm run perf
```

## Linux 流水线配置

在 Linux CI/CD 环境中运行 VSCode 测试需要特殊配置，因为涉及到图形界面和浏览器环境。

### 🐧 系统依赖安装

```bash
# Ubuntu/Debian 系统
sudo apt-get update
sudo apt-get install -y \
    libnss3-dev \
    libatk-bridge2.0-dev \
    libdrm2 \
    libxkbcommon-dev \
    libxcomposite-dev \
    libxdamage-dev \
    libxrandr-dev \
    libgbm-dev \
    libxss1 \
    libasound2-dev \
    xvfb \
    x11vnc \
    fluxbox

# CentOS/RHEL 系统
sudo yum install -y \
    nss \
    atk \
    at-spi2-atk \
    libdrm \
    libxkbcommon \
    libXcomposite \
    libXdamage \
    libXrandr \
    mesa-libgbm \
    libXScrnSaver \
    alsa-lib \
    xorg-x11-server-Xvfb
```

### 🖥️ 虚拟显示配置

```bash
# 启动虚拟显示服务器
export DISPLAY=:99
Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &

# 或者使用 xvfb-run 包装命令
xvfb-run -a --server-args="-screen 0 1024x768x24" npm run test-browser
```

### 🔧 环境变量配置

```bash
# 必需的环境变量
export DISPLAY=:99
export ELECTRON_DISABLE_SANDBOX=1
export ELECTRON_ENABLE_LOGGING=1

# 禁用 GPU 加速（避免在无 GPU 环境中出错）
export ELECTRON_DISABLE_GPU=1

# Chrome/Chromium 相关配置
export CHROME_DEVEL_SANDBOX=/usr/lib/chromium-browser/chrome-sandbox
export PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true

# 内存限制配置
export NODE_OPTIONS="--max-old-space-size=4096"

# 禁用共享内存（Docker 环境）
export ELECTRON_EXTRA_ARGS="--disable-dev-shm-usage --no-sandbox"
```

### 📦 Docker 配置示例

```dockerfile
FROM node:20-bullseye

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libnss3-dev \
    libatk-bridge2.0-dev \
    libdrm2 \
    libxkbcommon-dev \
    libxcomposite-dev \
    libxdamage-dev \
    libxrandr-dev \
    libgbm-dev \
    libxss1 \
    libasound2-dev \
    xvfb \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /workspace

# 复制项目文件
COPY package*.json ./
RUN npm ci

COPY . .

# 设置环境变量
ENV DISPLAY=:99
ENV ELECTRON_DISABLE_SANDBOX=1
ENV ELECTRON_DISABLE_GPU=1
ENV NODE_OPTIONS="--max-old-space-size=4096"

# 启动虚拟显示并运行测试
CMD ["sh", "-c", "Xvfb :99 -screen 0 1024x768x24 & npm run compile && npm run test"]
```

### 🚀 CI 配置示例

#### GitHub Actions

```yaml
name: VSCode Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        cache: 'npm'

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libnss3-dev libatk-bridge2.0-dev libdrm2 \
          libxkbcommon-dev libxcomposite-dev libxdamage-dev libxrandr-dev \
          libgbm-dev libxss1 libasound2-dev xvfb

    - name: Install dependencies
      run: npm ci

    - name: Compile
      run: npm run compile

    - name: Download Electron
      run: npm run electron

    - name: Install Playwright
      run: npm run playwright-install

    - name: Run unit tests
      run: xvfb-run -a npm run test-node

    - name: Run browser tests
      run: xvfb-run -a npm run test-browser

    - name: Run integration tests
      run: xvfb-run -a ./scripts/test-integration.sh
      env:
        ELECTRON_DISABLE_SANDBOX: 1
        ELECTRON_DISABLE_GPU: 1
```

#### GitLab CI

```yaml
stages:
  - test

variables:
  DISPLAY: ":99"
  ELECTRON_DISABLE_SANDBOX: "1"
  ELECTRON_DISABLE_GPU: "1"
  NODE_OPTIONS: "--max-old-space-size=4096"

test:
  stage: test
  image: node:20-bullseye

  before_script:
    - apt-get update -qq
    - apt-get install -y -qq libnss3-dev libatk-bridge2.0-dev libdrm2
      libxkbcommon-dev libxcomposite-dev libxdamage-dev libxrandr-dev
      libgbm-dev libxss1 libasound2-dev xvfb
    - Xvfb :99 -screen 0 1024x768x24 &
    - npm ci
    - npm run compile
    - npm run electron
    - npm run playwright-install

  script:
    - npm run test-node
    - npm run test-browser
    - ./scripts/test-integration.sh

  artifacts:
    reports:
      junit: test-results.xml
    paths:
      - .build/coverage/
    expire_in: 1 week
```

### ⚡ 性能优化建议

```bash
# 1. 并行执行测试
npm run test-node & npm run test-browser & wait

# 2. 缓存依赖
# 在 CI 中缓存 node_modules 和 .build 目录

# 3. 分层测试
# 先运行快速测试，失败时提前退出
npm run eslint && npm run test-node && npm run test-browser

# 4. 内存优化
export NODE_OPTIONS="--max-old-space-size=8192"

# 5. 使用 npm ci 而不是 npm install
npm ci --prefer-offline --no-audit
```

### 🔍 常见流水线问题解决

1. **Electron 无法启动**
   ```bash
   # 添加沙箱禁用参数
   export ELECTRON_DISABLE_SANDBOX=1
   export ELECTRON_EXTRA_ARGS="--no-sandbox --disable-dev-shm-usage"
   ```

2. **浏览器测试失败**
   ```bash
   # 确保 Xvfb 正在运行
   ps aux | grep Xvfb

   # 重新安装 Playwright 浏览器
   npx playwright install --with-deps
   ```

3. **内存不足**
   ```bash
   # 增加 Node.js 内存限制
   export NODE_OPTIONS="--max-old-space-size=8192"

   # 监控内存使用
   free -h
   ```

4. **权限问题**
   ```bash
   # 确保正确的文件权限
   chmod +x scripts/*.sh

   # 检查 Chrome 沙箱权限
   sudo chown root:root /usr/lib/chromium-browser/chrome-sandbox
   sudo chmod 4755 /usr/lib/chromium-browser/chrome-sandbox
   ```

### 📊 测试报告配置

```bash
# 生成 JUnit 格式报告
./scripts/test.sh --reporter mocha-junit-reporter \
  --reporter-options mochaFile=test-results.xml

# 生成覆盖率报告
./scripts/test.sh --coverage
# 报告位置：.build/coverage/lcov-report/index.html

# 合并多个测试报告
npm install -g junit-merge
junit-merge -d test-results/ -o merged-results.xml
```

## 调试和故障排除

### 调试技巧

1. **使用调试模式**
   ```bash
   ./scripts/test.sh --debug
   ```

2. **查看详细日志**
   ```bash
   ./scripts/test.sh --verbose
   ```

3. **运行单个测试**
   ```bash
   ./scripts/test.sh --grep "specific test name"
   ```

### 常见问题解决

1. **Electron 窗口不显示**
   - 检查是否使用了 `--debug` 参数
   - 确认 X11 转发配置（Linux 环境）

2. **测试超时**
   - 增加超时时间：`--timeout 30000`
   - 检查系统资源使用情况

3. **浏览器测试失败**
   - 重新安装 Playwright：`npm run playwright-install`
   - 检查浏览器版本兼容性

4. **覆盖率报告生成失败**
   - 确保有写入权限到 `.build/coverage` 目录
   - 检查磁盘空间

## 最佳实践

### 开发流程

1. **开发前**
   ```bash
   # 启动监听编译
   npm run watch
   ```

2. **开发中**
   ```bash
   # 运行相关单元测试
   ./scripts/test.sh --run path/to/your/test.ts
   ```

3. **提交前**
   ```bash
   # 运行完整检查
   npm run precommit
   npm run core-ci-pr
   ```

### 测试编写指南

1. **测试结构**
   ```typescript
   suite('ModuleName', () => {
       setup(() => {
           // 测试前准备
       });

       teardown(() => {
           // 测试后清理
       });

       test('should do something', () => {
           // 测试逻辑
       });
   });
   ```

2. **异步测试**
   ```typescript
   test('async operation', async () => {
       const result = await someAsyncOperation();
       assert.strictEqual(result, expected);
   });
   ```

3. **Mock 使用**
   ```typescript
   import * as sinon from 'sinon';

   test('with mock', () => {
       const stub = sinon.stub(service, 'method');
       stub.returns('mocked value');
       // 测试逻辑
       stub.restore();
   });
   ```

### 性能考虑

1. **并行执行**：单元测试支持并行执行
2. **资源清理**：及时清理测试资源
3. **测试隔离**：避免测试间相互影响
4. **合理超时**：设置合适的超时时间

### 测试数据管理

1. **测试数据隔离**
   ```bash
   # 使用临时目录
   export VSCODEUSERDATADIR=$(mktemp -d)
   ```

2. **测试工作区**
   ```bash
   # 使用专用测试工作区
   ./scripts/test-integration.sh --workspace test/fixtures/workspace
   ```

### 测试报告生成

VSCode 项目支持多种格式的测试报告，适用于不同的 CI/CD 平台和分析需求。

#### 1. **覆盖率报告**

```bash
# 生成完整覆盖率报告
./scripts/test.sh --coverage

# 指定覆盖率格式
./scripts/test.sh --coverage --coverageFormats lcov,html,json

# 自定义覆盖率输出路径
./scripts/test.sh --coverage --coveragePath .build/custom-coverage

# 生成单个测试的覆盖率
./scripts/test.sh --per-test-coverage --reporter full-json-stream

# 跳过特定失败的测试并生成覆盖率
./scripts/test.sh --coverage --grep "^(?!.*Comparers).*"
```

**覆盖率报告位置：**
- HTML 报告：`.build/coverage/lcov-report/index.html`
- LCOV 报告：`.build/coverage/lcov.info`
- JSON 报告：`.build/coverage/coverage-final.json`

#### 2. **JUnit XML 报告**

```bash
# 基础 JUnit 报告
./scripts/test.sh --reporter mocha-junit-reporter

# 自定义 JUnit 报告文件名
./scripts/test.sh --reporter mocha-junit-reporter \
  --reporter-options mochaFile=test-results.xml

# 带测试套件标题的 JUnit 报告
./scripts/test.sh --tfs "VSCode-Tests" \
  --reporter-options testsuitesTitle="VSCode Unit Tests"

# 集成测试的 JUnit 报告
./scripts/test-integration.sh --reporter mocha-junit-reporter
```

#### 3. **JSON 格式报告**

```bash
# 标准 JSON 报告
./scripts/test.sh --reporter json --reporter-options output=results.json

# 详细 JSON 流报告
./scripts/test.sh --reporter full-json-stream > detailed-results.json

# 自定义 JSON 报告
./scripts/test.sh --reporter json \
  --reporter-options output=custom-results.json,slow=100
```

#### 4. **多格式报告组合**

```bash
# 同时生成多种报告
./scripts/test.sh --reporter mocha-multi-reporters \
  --reporter-options configFile=reporter-config.json
```

**reporter-config.json 示例：**
```json
{
  "reporterEnabled": "spec,mocha-junit-reporter,json",
  "mochaJunitReporterReporterOptions": {
    "mochaFile": "test-results.xml"
  },
  "jsonReporterOptions": {
    "output": "test-results.json"
  }
}
```

#### 5. **浏览器测试报告**

```bash
# 浏览器测试 HTML 报告
npm run test-browser -- --reporter html --reporter-options output=browser-results.html

# 浏览器测试 JSON 报告
npm run test-browser -- --reporter json --reporter-options output=browser-results.json

# Playwright 测试报告
npm run test-browser -- --reporter=html,json
```

#### 6. **集成测试报告**

```bash
# 集成测试详细报告
./scripts/test-integration.sh --reporter spec > integration-results.txt

# 集成测试 JUnit 报告
INTEGRATION_TEST_REPORTER=mocha-junit-reporter ./scripts/test-integration.sh

# Web 集成测试报告
./scripts/test-web-integration.sh --browser chromium --reporter html
```

#### 7. **烟雾测试报告**

```bash
# 烟雾测试基础报告
npm run smoketest -- --reporter spec > smoke-test-results.txt

# 烟雾测试详细日志
npm run smoketest -- --verbose --reporter json > smoke-test-detailed.json

# Web 烟雾测试报告
npm run smoketest -- --web --browser chromium --reporter html
```

#### 8. **性能测试报告**

```bash
# 性能基准测试报告
npm run perf > performance-results.txt

# 详细性能分析
npm run perf -- --detailed --output performance-detailed.json
```

### 📊 CI/CD 集成报告配置

#### GitHub Actions 报告配置

```yaml
- name: Run tests with reports
  run: |
    # 创建报告目录
    mkdir -p test-reports coverage-reports

    # 运行测试并生成报告
    ./scripts/test.sh --coverage --reporter mocha-junit-reporter \
      --reporter-options mochaFile=test-reports/unit-tests.xml

    # 运行集成测试
    ./scripts/test-integration.sh --reporter mocha-junit-reporter \
      --reporter-options mochaFile=test-reports/integration-tests.xml

- name: Upload test reports
  uses: actions/upload-artifact@v3
  if: always()
  with:
    name: test-reports
    path: |
      test-reports/
      .build/coverage/

- name: Publish test results
  uses: dorny/test-reporter@v1
  if: always()
  with:
    name: VSCode Tests
    path: 'test-reports/*.xml'
    reporter: java-junit
```

#### GitLab CI 报告配置

```yaml
test:
  script:
    - mkdir -p test-reports coverage-reports
    - ./scripts/test.sh --coverage --reporter mocha-junit-reporter
      --reporter-options mochaFile=test-reports/unit-tests.xml
    - ./scripts/test-integration.sh --reporter mocha-junit-reporter
      --reporter-options mochaFile=test-reports/integration-tests.xml

  artifacts:
    reports:
      junit: test-reports/*.xml
      coverage_report:
        coverage_format: cobertura
        path: .build/coverage/cobertura-coverage.xml
    paths:
      - test-reports/
      - .build/coverage/
    expire_in: 1 week

  coverage: '/Lines\s*:\s*(\d+\.\d+)%/'
```

### 🔧 自定义报告器

#### 创建自定义报告器

```javascript
// custom-reporter.js
const mocha = require('mocha');

function CustomReporter(runner) {
  mocha.reporters.Base.call(this, runner);

  const tests = [];
  const failures = [];

  runner.on('test end', (test) => {
    tests.push({
      title: test.title,
      fullTitle: test.fullTitle(),
      duration: test.duration,
      state: test.state
    });
  });

  runner.on('fail', (test, err) => {
    failures.push({
      title: test.title,
      error: err.message,
      stack: err.stack
    });
  });

  runner.on('end', () => {
    const report = {
      stats: runner.stats,
      tests: tests,
      failures: failures,
      timestamp: new Date().toISOString()
    };

    console.log(JSON.stringify(report, null, 2));
  });
}

module.exports = CustomReporter;
```

使用自定义报告器：
```bash
./scripts/test.sh --reporter ./custom-reporter.js
```

### 📈 报告分析和可视化

#### 1. **覆盖率趋势分析**

```bash
# 生成覆盖率历史数据
./scripts/test.sh --coverage
echo "$(date),$(grep -o 'Lines.*[0-9.]*%' .build/coverage/lcov-report/index.html)" >> coverage-history.csv

# 使用 lcov 工具分析
lcov --summary .build/coverage/lcov.info
```

#### 2. **测试结果对比**

```bash
# 比较两次测试结果
diff previous-results.json current-results.json > test-diff.txt

# 生成测试趋势报告
node scripts/analyze-test-trends.js test-reports/
```

#### 3. **性能回归检测**

```bash
# 性能基准对比
npm run perf -- --baseline previous-perf.json --output current-perf.json
node scripts/compare-performance.js previous-perf.json current-perf.json
```

### 🎯 报告最佳实践

#### 1. **报告文件组织**

```
project-root/
├── test-reports/
│   ├── unit-tests.xml          # 单元测试 JUnit 报告
│   ├── integration-tests.xml   # 集成测试 JUnit 报告
│   ├── smoke-tests.json        # 烟雾测试 JSON 报告
│   └── performance.json        # 性能测试报告
├── coverage-reports/
│   ├── lcov.info              # LCOV 格式覆盖率
│   ├── coverage-final.json    # JSON 格式覆盖率
│   └── html/                  # HTML 覆盖率报告
└── artifacts/
    ├── screenshots/           # 失败测试截图
    └── logs/                  # 测试日志
```

#### 2. **报告生成脚本**

```bash
#!/bin/bash
# generate-reports.sh

set -e

# 创建报告目录
mkdir -p test-reports coverage-reports artifacts/logs

# 设置时间戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

echo "🚀 开始生成测试报告..."

# 1. 单元测试报告
echo "📋 生成单元测试报告..."
./scripts/test.sh --coverage --reporter mocha-junit-reporter \
  --reporter-options mochaFile=test-reports/unit-tests-${TIMESTAMP}.xml \
  2>&1 | tee artifacts/logs/unit-tests.log

# 2. 集成测试报告
echo "🔗 生成集成测试报告..."
./scripts/test-integration.sh --reporter mocha-junit-reporter \
  --reporter-options mochaFile=test-reports/integration-tests-${TIMESTAMP}.xml \
  2>&1 | tee artifacts/logs/integration-tests.log

# 3. 浏览器测试报告
echo "🌐 生成浏览器测试报告..."
npm run test-browser -- --reporter json \
  --reporter-options output=test-reports/browser-tests-${TIMESTAMP}.json \
  2>&1 | tee artifacts/logs/browser-tests.log

# 4. 代码质量报告
echo "✅生成代码质量报告..."
npm run eslint -- --format json --output-file test-reports/eslint-${TIMESTAMP}.json || true
npm run stylelint -- --formatter json --output-file test-reports/stylelint-${TIMESTAMP}.json || true

# 5. 生成汇总报告
echo "📊 生成汇总报告..."
node scripts/generate-summary-report.js test-reports/ > test-reports/summary-${TIMESTAMP}.html

echo "✅ 所有报告生成完成！"
echo "📁 报告位置："
echo "   - 测试报告: test-reports/"
echo "   - 覆盖率报告: .build/coverage/"
echo "   - 日志文件: artifacts/logs/"
```

#### 3. **报告清理脚本**

```bash
#!/bin/bash
# cleanup-old-reports.sh

# 保留最近 10 次的报告
find test-reports/ -name "*.xml" -type f -mtime +10 -delete
find test-reports/ -name "*.json" -type f -mtime +10 -delete
find artifacts/logs/ -name "*.log" -type f -mtime +7 -delete

echo "🧹 旧报告清理完成"
```

## 快速参考

### 常用测试命令

```bash
# 快速单元测试
./scripts/test.sh --grep "your-test-pattern"

# 快速集成测试
./scripts/test-integration.sh

# 快速烟雾测试
npm run smoketest -- -f "Basic"

# 代码检查
npm run eslint && npm run stylelint

# 完整 CI 流程
npm run core-ci
```

### 测试环境变量

| 变量名 | 描述 | 示例值 |
|--------|------|--------|
| `VSCODEUSERDATADIR` | 用户数据目录 | `/tmp/vscode-test-data` |
| `VSCODECRASHDIR` | 崩溃报告目录 | `.build/crashes` |
| `VSCODELOGSDIR` | 日志目录 | `.build/logs` |
| `INTEGRATION_TEST_ELECTRON_PATH` | 测试用 Electron 路径 | `./scripts/code.sh` |
| `DEBUG` | 调试日志级别 | `pw:browser` |

### 故障排除检查清单

- [ ] 依赖是否已安装：`npm install`
- [ ] 代码是否已编译：`npm run compile`
- [ ] Electron 是否已下载：`npm run electron`
- [ ] Playwright 是否已安装：`npm run playwright-install`
- [ ] 环境变量是否正确设置
- [ ] 磁盘空间是否充足
- [ ] 权限是否正确

---

## 相关文档

- [单元测试详细说明](../test/unit/README.md)
- [集成测试详细说明](../test/integration/browser/README.md)
- [烟雾测试详细说明](../test/smoke/README.md)
- [贡献指南](../CONTRIBUTING.md)
- [构建说明](../README.md#build-and-run)

## 联系和支持

如果在测试过程中遇到问题，可以：

1. 查看 [GitHub Issues](https://github.com/microsoft/vscode/issues)
2. 参考 [VSCode 开发文档](https://code.visualstudio.com/api)
3. 加入 [VSCode 开发者社区](https://github.com/microsoft/vscode/discussions)
